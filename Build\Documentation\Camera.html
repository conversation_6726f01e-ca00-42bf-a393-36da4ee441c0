<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Camera - Cesium Documentation</title>

    <!--[if lt IE 9]>
      <script src="javascript/html5.js"></script>
    <![endif]-->
    <link href="styles/jsdoc-default.css" rel="stylesheet">
    <link href="styles/prism.css" rel="stylesheet">
</head>
<body>

<div id="main">

    <h1 class="page-title">
        <a href="index.html"><img src="Images/CesiumLogo.png" class="cesiumLogo"></a>
        Camera
        <div class="titleCenterer"></div>
    </h1>

    




<section>

<header>
    
</header>

<article>
    <div class="container-overview">
    

    
        
    <div class="nameContainer">
    <h4 class="name" id="Camera">
        <a href="#Camera" class="doc-link"></a>
        new Cesium.Camera<span class="signature">(scene)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L81">engine/Source/Scene/Camera.js 81</a>
</div>


    </h4>

    </div>

    


<div class="description">
    The camera is defined by a position, orientation, and view frustum.
<br /><br />
The orientation forms an orthonormal basis with a view, up and right = view x up unit vectors.
<br /><br />
The viewing frustum is defined by 6 planes.
Each plane is represented by a <a href="Cartesian4.html"><code>Cartesian4</code></a> object, where the x, y, and z components
define the unit vector normal to the plane, and the w component is the distance of the
plane from the origin/camera position.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>scene</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Scene.html">Scene</a></span>


            
            </td>

            

            <td class="description last">
            
                The scene.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">// Create a camera looking down the negative z-axis, positioned at the origin,
// with a field of view of 60 degrees, and 1:1 aspect ratio.
const camera = new Cesium.Camera(scene);
camera.position = new Cesium.Cartesian3();
camera.direction = Cesium.Cartesian3.negate(Cesium.Cartesian3.UNIT_Z, new Cesium.Cartesian3());
camera.up = Cesium.Cartesian3.clone(Cesium.Cartesian3.UNIT_Y);
camera.frustum.fov = Cesium.Math.PI_OVER_THREE;
camera.frustum.near = 1.0;
camera.frustum.far = 2.0;</code></pre>

    

    

    
    <h5>Demo:</h5>
    <ul class="see-list">
        <li><a href="https://sandcastle.cesium.com/index.html?src=Camera.html">Cesium Sandcastle Camera Demo</a></li>
    
        <li><a href="https://sandcastle.cesium.com/index.html?src=Camera%2520Tutorial.html">Cesium Sandcastle Camera Tutorial Example</a></li>
    
        <li><a href="https://cesium.com/learn/cesiumjs-learn/cesiumjs-camera">Camera Tutorial</a></li>
    </ul>
    

    

    

    
</dl>


    
    </div>

    

    

    

    

    

    
        <h3 class="subsection-title">Members</h3>

        
            
<div class="nameContainer">
<h4 class="name" id=".DEFAULT_OFFSET">
    <a href="#.DEFAULT_OFFSET" class="doc-link"></a>
    <span class="type-signature attribute-static">static</span> Cesium.Camera.DEFAULT_OFFSET<span class="type-signature"> : <a href="HeadingPitchRange.html">HeadingPitchRange</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L309">engine/Source/Scene/Camera.js 309</a>
</div>


</h4>

</div>



<div class="description">
    The default heading/pitch/range that is used when the camera flies to a location that contains a bounding sphere.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id=".DEFAULT_VIEW_FACTOR">
    <a href="#.DEFAULT_VIEW_FACTOR" class="doc-link"></a>
    <span class="type-signature attribute-static">static</span> Cesium.Camera.DEFAULT_VIEW_FACTOR<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L303">engine/Source/Scene/Camera.js 303</a>
</div>


</h4>

</div>



<div class="description">
    A scalar to multiply to the camera position and add it back after setting the camera to view the rectangle.
A value of zero means the camera will view the entire <code>Camera#DEFAULT_VIEW_RECTANGLE</code>, a value greater than zero
will move it further away from the extent, and a value less than zero will move it close to the extent.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id=".DEFAULT_VIEW_RECTANGLE">
    <a href="#.DEFAULT_VIEW_RECTANGLE" class="doc-link"></a>
    <span class="type-signature attribute-static">static</span> Cesium.Camera.DEFAULT_VIEW_RECTANGLE<span class="type-signature"> : <a href="Rectangle.html">Rectangle</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L290">engine/Source/Scene/Camera.js 290</a>
</div>


</h4>

</div>



<div class="description">
    The default rectangle the camera will view on creation.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="changed">
    <a href="#changed" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> changed<span class="type-signature"> : <a href="Event.html">Event</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1109">engine/Source/Scene/Camera.js 1109</a>
</div>


</h4>

</div>



<div class="description">
    Gets the event that will be raised when the camera has changed by <code>percentageChanged</code>.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="constrainedAxis">
    <a href="#constrainedAxis" class="doc-link"></a>
    constrainedAxis<span class="type-signature"> : <a href="Cartesian3.html">Cartesian3</a>|undefined</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L203">engine/Source/Scene/Camera.js 203</a>
</div>


</h4>

</div>



<div class="description">
    If set, the camera will not be able to rotate past this axis in either direction.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">undefined</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="defaultLookAmount">
    <a href="#defaultLookAmount" class="doc-link"></a>
    defaultLookAmount<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L183">engine/Source/Scene/Camera.js 183</a>
</div>


</h4>

</div>



<div class="description">
    The default amount to rotate the camera when an argument is not
provided to the look methods.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">Math.PI / 60.0</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="defaultMoveAmount">
    <a href="#defaultMoveAmount" class="doc-link"></a>
    defaultMoveAmount<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L176">engine/Source/Scene/Camera.js 176</a>
</div>


</h4>

</div>



<div class="description">
    The default amount to move the camera when an argument is not
provided to the move methods.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">100000.0;</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="defaultRotateAmount">
    <a href="#defaultRotateAmount" class="doc-link"></a>
    defaultRotateAmount<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L190">engine/Source/Scene/Camera.js 190</a>
</div>


</h4>

</div>



<div class="description">
    The default amount to rotate the camera when an argument is not
provided to the rotate methods.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">Math.PI / 3600.0</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="defaultZoomAmount">
    <a href="#defaultZoomAmount" class="doc-link"></a>
    defaultZoomAmount<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L197">engine/Source/Scene/Camera.js 197</a>
</div>


</h4>

</div>



<div class="description">
    The default amount to move the camera when an argument is not
provided to the zoom methods.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">100000.0;</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="direction">
    <a href="#direction" class="doc-link"></a>
    direction<span class="type-signature"> : <a href="Cartesian3.html">Cartesian3</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L133">engine/Source/Scene/Camera.js 133</a>
</div>


</h4>

</div>



<div class="description">
    The view direction of the camera.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="directionWC">
    <a href="#directionWC" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> directionWC<span class="type-signature"> : <a href="Cartesian3.html">Cartesian3</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L951">engine/Source/Scene/Camera.js 951</a>
</div>


</h4>

</div>



<div class="description">
    Gets the view direction of the camera in world coordinates.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="frustum">
    <a href="#frustum" class="doc-link"></a>
    frustum<span class="type-signature"> : <a href="PerspectiveFrustum.html">PerspectiveFrustum</a>|<a href="PerspectiveOffCenterFrustum.html">PerspectiveOffCenterFrustum</a>|<a href="OrthographicFrustum.html">OrthographicFrustum</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L165">engine/Source/Scene/Camera.js 165</a>
</div>


</h4>

</div>



<div class="description">
    The region of space in view.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">PerspectiveFrustum()</code>
    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="PerspectiveFrustum.html">PerspectiveFrustum</a></li>
    
        <li><a href="PerspectiveOffCenterFrustum.html">PerspectiveOffCenterFrustum</a></li>
    
        <li><a href="OrthographicFrustum.html">OrthographicFrustum</a></li>
    </ul>
    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="heading">
    <a href="#heading" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> heading<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L993">engine/Source/Scene/Camera.js 993</a>
</div>


</h4>

</div>



<div class="description">
    Gets the camera heading in radians.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="inverseTransform">
    <a href="#inverseTransform" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> inverseTransform<span class="type-signature"> : <a href="Matrix4.html">Matrix4</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L874">engine/Source/Scene/Camera.js 874</a>
</div>


</h4>

</div>



<div class="description">
    Gets the inverse camera transform.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript"><a href="Matrix4.html#.IDENTITY"><code>Matrix4.IDENTITY</code></a></code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="inverseViewMatrix">
    <a href="#inverseViewMatrix" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> inverseViewMatrix<span class="type-signature"> : <a href="Matrix4.html">Matrix4</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L906">engine/Source/Scene/Camera.js 906</a>
</div>


</h4>

</div>



<div class="description">
    Gets the inverse view matrix.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#viewMatrix">Camera#viewMatrix</a></li>
    </ul>
    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="maximumZoomFactor">
    <a href="#maximumZoomFactor" class="doc-link"></a>
    maximumZoomFactor<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L210">engine/Source/Scene/Camera.js 210</a>
</div>


</h4>

</div>



<div class="description">
    The factor multiplied by the the map size used to determine where to clamp the camera position
when zooming out from the surface. The default is 1.5. Only valid for 2D and the map is rotatable.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">1.5</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="moveEnd">
    <a href="#moveEnd" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> moveEnd<span class="type-signature"> : <a href="Event.html">Event</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1097">engine/Source/Scene/Camera.js 1097</a>
</div>


</h4>

</div>



<div class="description">
    Gets the event that will be raised when the camera has stopped moving.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="moveStart">
    <a href="#moveStart" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> moveStart<span class="type-signature"> : <a href="Event.html">Event</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1085">engine/Source/Scene/Camera.js 1085</a>
</div>


</h4>

</div>



<div class="description">
    Gets the event that will be raised at when the camera starts to move.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="percentageChanged">
    <a href="#percentageChanged" class="doc-link"></a>
    percentageChanged<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L227">engine/Source/Scene/Camera.js 227</a>
</div>


</h4>

</div>



<div class="description">
    The amount the camera has to change before the <code>changed</code> event is raised. The value is a percentage in the [0, 1] range.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">0.5</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="pitch">
    <a href="#pitch" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> pitch<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1024">engine/Source/Scene/Camera.js 1024</a>
</div>


</h4>

</div>



<div class="description">
    Gets the camera pitch in radians.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="position">
    <a href="#position" class="doc-link"></a>
    position<span class="type-signature"> : <a href="Cartesian3.html">Cartesian3</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L100">engine/Source/Scene/Camera.js 100</a>
</div>


</h4>

</div>



<div class="description">
    The position of the camera.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="positionCartographic">
    <a href="#positionCartographic" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> positionCartographic<span class="type-signature"> : <a href="Cartographic.html">Cartographic</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L923">engine/Source/Scene/Camera.js 923</a>
</div>


</h4>

</div>



<div class="description">
    Gets the <a href="Cartographic.html"><code>Cartographic</code></a> position of the camera, with longitude and latitude
expressed in radians and height in meters.  In 2D and Columbus View, it is possible
for the returned longitude and latitude to be outside the range of valid longitudes
and latitudes when the camera is outside the map.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="positionWC">
    <a href="#positionWC" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> positionWC<span class="type-signature"> : <a href="Cartesian3.html">Cartesian3</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L937">engine/Source/Scene/Camera.js 937</a>
</div>


</h4>

</div>



<div class="description">
    Gets the position of the camera in world coordinates.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="right">
    <a href="#right" class="doc-link"></a>
    right<span class="type-signature"> : <a href="Cartesian3.html">Cartesian3</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L151">engine/Source/Scene/Camera.js 151</a>
</div>


</h4>

</div>



<div class="description">
    The right direction of the camera.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="rightWC">
    <a href="#rightWC" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> rightWC<span class="type-signature"> : <a href="Cartesian3.html">Cartesian3</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L979">engine/Source/Scene/Camera.js 979</a>
</div>


</h4>

</div>



<div class="description">
    Gets the right direction of the camera in world coordinates.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="roll">
    <a href="#roll" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> roll<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1055">engine/Source/Scene/Camera.js 1055</a>
</div>


</h4>

</div>



<div class="description">
    Gets the camera roll in radians.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="transform">
    <a href="#transform" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> transform<span class="type-signature"> : <a href="Matrix4.html">Matrix4</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L859">engine/Source/Scene/Camera.js 859</a>
</div>


</h4>

</div>



<div class="description">
    Gets the camera's reference frame. The inverse of this transformation is appended to the view matrix.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript"><a href="Matrix4.html#.IDENTITY"><code>Matrix4.IDENTITY</code></a></code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="up">
    <a href="#up" class="doc-link"></a>
    up<span class="type-signature"> : <a href="Cartesian3.html">Cartesian3</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L142">engine/Source/Scene/Camera.js 142</a>
</div>


</h4>

</div>



<div class="description">
    The up direction of the camera.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="upWC">
    <a href="#upWC" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> upWC<span class="type-signature"> : <a href="Cartesian3.html">Cartesian3</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L965">engine/Source/Scene/Camera.js 965</a>
</div>


</h4>

</div>



<div class="description">
    Gets the up direction of the camera in world coordinates.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="viewMatrix">
    <a href="#viewMatrix" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> viewMatrix<span class="type-signature"> : <a href="Matrix4.html">Matrix4</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L890">engine/Source/Scene/Camera.js 890</a>
</div>


</h4>

</div>



<div class="description">
    Gets the view matrix.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#inverseViewMatrix">Camera#inverseViewMatrix</a></li>
    </ul>
    

    
</dl>


        
    

    
        <h3 class="subsection-title">Methods</h3>

        
            
    <div class="nameContainer">
    <h4 class="name" id="cameraToWorldCoordinates">
        <a href="#cameraToWorldCoordinates" class="doc-link"></a>
        cameraToWorldCoordinates<span class="signature">(cartesian, <span class="optional">result</span>)</span> &rarr; <span class="type-signature returnType"><a href="Cartesian4.html">Cartesian4</a></span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1672">engine/Source/Scene/Camera.js 1672</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Transform a vector or point from the camera's reference frame to world coordinates.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>cartesian</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian4.html">Cartesian4</a></span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The vector or point to transform.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>result</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian4.html">Cartesian4</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The object onto which to store the result.</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    The transformed vector or point.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="cameraToWorldCoordinatesPoint">
        <a href="#cameraToWorldCoordinatesPoint" class="doc-link"></a>
        cameraToWorldCoordinatesPoint<span class="signature">(cartesian, <span class="optional">result</span>)</span> &rarr; <span class="type-signature returnType"><a href="Cartesian3.html">Cartesian3</a></span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1693">engine/Source/Scene/Camera.js 1693</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Transform a point from the camera's reference frame to world coordinates.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>cartesian</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The point to transform.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>result</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The object onto which to store the result.</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    The transformed point.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="cameraToWorldCoordinatesVector">
        <a href="#cameraToWorldCoordinatesVector" class="doc-link"></a>
        cameraToWorldCoordinatesVector<span class="signature">(cartesian, <span class="optional">result</span>)</span> &rarr; <span class="type-signature returnType"><a href="Cartesian3.html">Cartesian3</a></span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1714">engine/Source/Scene/Camera.js 1714</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Transform a vector from the camera's reference frame to world coordinates.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>cartesian</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The vector to transform.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>result</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The object onto which to store the result.</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    The transformed vector.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="cancelFlight">
        <a href="#cancelFlight" class="doc-link"></a>
        cancelFlight<span class="signature">()</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L3259">engine/Source/Scene/Camera.js 3259</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Cancels the current camera flight and leaves the camera at its current location.
If no flight is in progress, this function does nothing.
</div>

























<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="completeFlight">
        <a href="#completeFlight" class="doc-link"></a>
        completeFlight<span class="signature">()</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L3270">engine/Source/Scene/Camera.js 3270</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Completes the current camera flight and moves the camera immediately to its final destination.
If no flight is in progress, this function does nothing.
</div>

























<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="computeViewRectangle">
        <a href="#computeViewRectangle" class="doc-link"></a>
        computeViewRectangle<span class="signature">(<span class="optional">ellipsoid</span>, <span class="optional">result</span>)</span> &rarr; <span class="type-signature returnType"><a href="Rectangle.html">Rectangle</a>|undefined</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L3806">engine/Source/Scene/Camera.js 3806</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Computes the approximate visible rectangle on the ellipsoid.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>ellipsoid</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Ellipsoid.html">Ellipsoid</a></span>


            
            </td>

            
                <td class="default">
                
                    <code class="language-javascript">Ellipsoid.default</code>
                
                </td>
            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The ellipsoid that you want to know the visible region.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>result</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Rectangle.html">Rectangle</a></span>


            
            </td>

            
                <td class="default">
                
                </td>
            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The rectangle in which to store the result</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    The visible rectangle or undefined if the ellipsoid isn't visible at all.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="distanceToBoundingSphere">
        <a href="#distanceToBoundingSphere" class="doc-link"></a>
        distanceToBoundingSphere<span class="signature">(boundingSphere)</span> &rarr; <span class="type-signature returnType">number</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L3056">engine/Source/Scene/Camera.js 3056</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Return the distance from the camera to the front of the bounding sphere.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>boundingSphere</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="BoundingSphere.html">BoundingSphere</a></span>


            
            </td>

            

            <td class="description last">
            
                The bounding sphere in world coordinates.</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    The distance to the bounding sphere.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="flyHome">
        <a href="#flyHome" class="doc-link"></a>
        flyHome<span class="signature">(<span class="optional">duration</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1548">engine/Source/Scene/Camera.js 1548</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Fly the camera to the home view.  Use <code>Camera#.DEFAULT_VIEW_RECTANGLE</code> to set
the default view for the 3D scene.  The home view for 2D and columbus view shows the
entire map.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>duration</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The duration of the flight in seconds. If omitted, Cesium attempts to calculate an ideal duration based on the distance to be traveled by the flight. See <a href="Camera.html#flyTo"><code>Camera#flyTo</code></a></td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="flyTo">
        <a href="#flyTo" class="doc-link"></a>
        flyTo<span class="signature">(options)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L3349">engine/Source/Scene/Camera.js 3349</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Flies the camera from its current position to a new position.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            <td class="description last">
            
                Object with the following properties:
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>destination</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>
|

<span class="param-type"><a href="Rectangle.html">Rectangle</a></span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The final position of the camera in world coordinates or a rectangle that would be visible from a top-down view.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>orientation</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                An object that contains either direction and up properties or heading, pitch and roll properties. By default, the direction will point
towards the center of the frame in 3D and in the negative z direction in Columbus view. The up direction will point towards local north in 3D and in the positive
y direction in Columbus view.  Orientation is not used in 2D when in infinite scrolling mode.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>duration</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The duration of the flight in seconds. If omitted, Cesium attempts to calculate an ideal duration based on the distance to be traveled by the flight.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>complete</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Camera.html#.FlightCompleteCallback">Camera.FlightCompleteCallback</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The function to execute when the flight is complete.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>cancel</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Camera.html#.FlightCancelledCallback">Camera.FlightCancelledCallback</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The function to execute if the flight is cancelled.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>endTransform</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Matrix4.html">Matrix4</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                Transform matrix representing the reference frame the camera will be in when the flight is completed.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>maximumHeight</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The maximum height at the peak of the flight.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>pitchAdjustHeight</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                If camera flyes higher than that value, adjust pitch duiring the flight to look down, and keep Earth in viewport.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>flyOverLongitude</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                There are always two ways between 2 points on globe. This option force camera to choose fight direction to fly over that longitude.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>flyOverLongitudeWeight</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                Fly over the lon specifyed via flyOverLongitude only if that way is not longer than short way times flyOverLongitudeWeight.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>convert</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                Whether to convert the destination from world coordinates to scene coordinates (only relevant when not using 3D). Defaults to <code>true</code>.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>easingFunction</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="EasingFunction.html#.Callback">EasingFunction.Callback</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                Controls how the time is interpolated over the duration of the flight.</td>
        </tr>

    
    </tbody>
</table>
            </td>
        </tr>

    
    </tbody>
</table>













<h5>Throws:</h5>
<ul>
        <li>

    <div class="param-desc">
        
<span class="param-type"><a href="DeveloperError.html">DeveloperError</a></span>

: If either direction or up is given, then both are required.
    </div>

</li>
    </ul>



<dl class="details">


    

    

    

    

    

    

    

    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">// 1. Fly to a position with a top-down view
viewer.camera.flyTo({
    destination : Cesium.Cartesian3.fromDegrees(-117.16, 32.71, 15000.0)
});

// 2. Fly to a Rectangle with a top-down view
viewer.camera.flyTo({
    destination : Cesium.Rectangle.fromDegrees(west, south, east, north)
});

// 3. Fly to a position with an orientation using unit vectors.
viewer.camera.flyTo({
    destination : Cesium.Cartesian3.fromDegrees(-122.19, 46.25, 5000.0),
    orientation : {
        direction : new Cesium.Cartesian3(-0.04231243104240401, -0.20123236049443421, -0.97862924300734),
        up : new Cesium.Cartesian3(-0.47934589305293746, -0.8553216253114552, 0.1966022179118339)
    }
});

// 4. Fly to a position with an orientation using heading, pitch and roll.
viewer.camera.flyTo({
    destination : Cesium.Cartesian3.fromDegrees(-122.19, 46.25, 5000.0),
    orientation : {
        heading : Cesium.Math.toRadians(175.0),
        pitch : Cesium.Math.toRadians(-35.0),
        roll : 0.0
    }
});</code></pre>

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="flyToBoundingSphere">
        <a href="#flyToBoundingSphere" class="doc-link"></a>
        flyToBoundingSphere<span class="signature">(boundingSphere, <span class="optional">options</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L3584">engine/Source/Scene/Camera.js 3584</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Flies the camera to a location where the current view contains the provided bounding sphere.

<p> The offset is heading/pitch/range in the local east-north-up reference frame centered at the center of the bounding sphere.
The heading and the pitch angles are defined in the local east-north-up reference frame.
The heading is the angle from y axis and increasing towards the x axis. Pitch is the rotation from the xy-plane. Positive pitch
angles are below the plane. Negative pitch angles are above the plane. The range is the distance from the center. If the range is
zero, a range will be computed such that the whole bounding sphere is visible.</p>

<p>In 2D and Columbus View, there must be a top down view. The camera will be placed above the target looking down. The height above the
target will be the range. The heading will be aligned to local north.</p>
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>boundingSphere</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="BoundingSphere.html">BoundingSphere</a></span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The bounding sphere to view, in world coordinates.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                Object with the following properties:
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>duration</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The duration of the flight in seconds. If omitted, Cesium attempts to calculate an ideal duration based on the distance to be traveled by the flight.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>offset</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="HeadingPitchRange.html">HeadingPitchRange</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The offset from the target in the local east-north-up reference frame centered at the target.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>complete</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Camera.html#.FlightCompleteCallback">Camera.FlightCompleteCallback</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The function to execute when the flight is complete.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>cancel</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Camera.html#.FlightCancelledCallback">Camera.FlightCancelledCallback</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The function to execute if the flight is cancelled.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>endTransform</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Matrix4.html">Matrix4</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                Transform matrix representing the reference frame the camera will be in when the flight is completed.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>maximumHeight</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The maximum height at the peak of the flight.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>pitchAdjustHeight</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                If camera flyes higher than that value, adjust pitch duiring the flight to look down, and keep Earth in viewport.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>flyOverLongitude</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                There are always two ways between 2 points on globe. This option force camera to choose fight direction to fly over that longitude.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>flyOverLongitudeWeight</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                Fly over the lon specifyed via flyOverLongitude only if that way is not longer than short way times flyOverLongitudeWeight.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>easingFunction</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="EasingFunction.html#.Callback">EasingFunction.Callback</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                Controls how the time is interpolated over the duration of the flight.</td>
        </tr>

    
    </tbody>
</table>
            </td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="getMagnitude">
        <a href="#getMagnitude" class="doc-link"></a>
        getMagnitude<span class="signature">()</span> &rarr; <span class="type-signature returnType">number</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L2286">engine/Source/Scene/Camera.js 2286</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Gets the magnitude of the camera position. In 3D, this is the vector magnitude. In 2D and
Columbus view, this is the distance to the map.
</div>





















<h5>Returns:</h5>

        
<div class="param-desc">
    The magnitude of the position.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="getPickRay">
        <a href="#getPickRay" class="doc-link"></a>
        getPickRay<span class="signature">(windowPosition, <span class="optional">result</span>)</span> &rarr; <span class="type-signature returnType"><a href="Ray.html">Ray</a>|undefined</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L3019">engine/Source/Scene/Camera.js 3019</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Create a ray from the camera position through the pixel at <code>windowPosition</code>
in world coordinates.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>windowPosition</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian2.html">Cartesian2</a></span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The x and y coordinates of a pixel.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>result</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Ray.html">Ray</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The object onto which to store the result.</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    Returns the <a href="Cartesian3.html"><code>Cartesian3</code></a> position and direction of the ray, or undefined if the pick ray cannot be determined.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="getPixelSize">
        <a href="#getPixelSize" class="doc-link"></a>
        getPixelSize<span class="signature">(boundingSphere, drawingBufferWidth, drawingBufferHeight)</span> &rarr; <span class="type-signature returnType">number</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L3086">engine/Source/Scene/Camera.js 3086</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Return the pixel size in meters.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>boundingSphere</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="BoundingSphere.html">BoundingSphere</a></span>


            
            </td>

            

            <td class="description last">
            
                The bounding sphere in world coordinates.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>drawingBufferWidth</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                The drawing buffer width.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>drawingBufferHeight</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                The drawing buffer height.</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    The pixel size in meters.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="getRectangleCameraCoordinates">
        <a href="#getRectangleCameraCoordinates" class="doc-link"></a>
        getRectangleCameraCoordinates<span class="signature">(rectangle, <span class="optional">result</span>)</span> &rarr; <span class="type-signature returnType"><a href="Cartesian3.html">Cartesian3</a></span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L2814">engine/Source/Scene/Camera.js 2814</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Get the camera position needed to view a rectangle on an ellipsoid or map
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>rectangle</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Rectangle.html">Rectangle</a></span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The rectangle to view.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>result</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The camera position needed to view the rectangle</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    The camera position needed to view the rectangle
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="look">
        <a href="#look" class="doc-link"></a>
        look<span class="signature">(axis, <span class="optional">angle</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1965">engine/Source/Scene/Camera.js 1965</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Rotate each of the camera's orientation vectors around <code>axis</code> by <code>angle</code>
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>axis</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The axis to rotate around.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>angle</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The angle, in radians, to rotate by. Defaults to <code>defaultLookAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#lookUp">Camera#lookUp</a></li>
    
        <li><a href="Camera.html#lookDown">Camera#lookDown</a></li>
    
        <li><a href="Camera.html#lookLeft">Camera#lookLeft</a></li>
    
        <li><a href="Camera.html#lookRight">Camera#lookRight</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="lookAt">
        <a href="#lookAt" class="doc-link"></a>
        lookAt<span class="signature">(target, offset)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L2330">engine/Source/Scene/Camera.js 2330</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Sets the camera position and orientation using a target and offset. The target must be given in
world coordinates. The offset can be either a cartesian or heading/pitch/range in the local east-north-up reference frame centered at the target.
If the offset is a cartesian, then it is an offset from the center of the reference frame defined by the transformation matrix. If the offset
is heading/pitch/range, then the heading and the pitch angles are defined in the reference frame defined by the transformation matrix.
The heading is the angle from y axis and increasing towards the x axis. Pitch is the rotation from the xy-plane. Positive pitch
angles are below the plane. Negative pitch angles are above the plane. The range is the distance from the center.

In 2D, there must be a top down view. The camera will be placed above the target looking down. The height above the
target will be the magnitude of the offset. The heading will be determined from the offset. If the heading cannot be
determined from the offset, the heading will be north.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>target</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>


            
            </td>

            

            <td class="description last">
            
                The target position in world coordinates.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>offset</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>
|

<span class="param-type"><a href="HeadingPitchRange.html">HeadingPitchRange</a></span>


            
            </td>

            

            <td class="description last">
            
                The offset from the target in the local east-north-up reference frame centered at the target.</td>
        </tr>

    
    </tbody>
</table>













<h5>Throws:</h5>
<ul>
        <li>

    <div class="param-desc">
        
<span class="param-type"><a href="DeveloperError.html">DeveloperError</a></span>

: lookAt is not supported while morphing.
    </div>

</li>
    </ul>



<dl class="details">


    

    

    

    

    

    

    

    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">// 1. Using a cartesian offset
const center = Cesium.Cartesian3.fromDegrees(-98.0, 40.0);
viewer.camera.lookAt(center, new Cesium.Cartesian3(0.0, -4790000.0, 3930000.0));

// 2. Using a HeadingPitchRange offset
const center = Cesium.Cartesian3.fromDegrees(-72.0, 40.0);
const heading = Cesium.Math.toRadians(50.0);
const pitch = Cesium.Math.toRadians(-20.0);
const range = 5000.0;
viewer.camera.lookAt(center, new Cesium.HeadingPitchRange(heading, pitch, range));</code></pre>

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="lookAtTransform">
        <a href="#lookAtTransform" class="doc-link"></a>
        lookAtTransform<span class="signature">(transform, <span class="optional">offset</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L2421">engine/Source/Scene/Camera.js 2421</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Sets the camera position and orientation using a target and transformation matrix. The offset can be either a cartesian or heading/pitch/range.
If the offset is a cartesian, then it is an offset from the center of the reference frame defined by the transformation matrix. If the offset
is heading/pitch/range, then the heading and the pitch angles are defined in the reference frame defined by the transformation matrix.
The heading is the angle from y axis and increasing towards the x axis. Pitch is the rotation from the xy-plane. Positive pitch
angles are below the plane. Negative pitch angles are above the plane. The range is the distance from the center.

In 2D, there must be a top down view. The camera will be placed above the center of the reference frame. The height above the
target will be the magnitude of the offset. The heading will be determined from the offset. If the heading cannot be
determined from the offset, the heading will be north.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>transform</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Matrix4.html">Matrix4</a></span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The transformation matrix defining the reference frame.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>offset</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>
|

<span class="param-type"><a href="HeadingPitchRange.html">HeadingPitchRange</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The offset from the target in a reference frame centered at the target.</td>
        </tr>

    
    </tbody>
</table>













<h5>Throws:</h5>
<ul>
        <li>

    <div class="param-desc">
        
<span class="param-type"><a href="DeveloperError.html">DeveloperError</a></span>

: lookAtTransform is not supported while morphing.
    </div>

</li>
    </ul>



<dl class="details">


    

    

    

    

    

    

    

    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">// 1. Using a cartesian offset
const transform = Cesium.Transforms.eastNorthUpToFixedFrame(Cesium.Cartesian3.fromDegrees(-98.0, 40.0));
viewer.camera.lookAtTransform(transform, new Cesium.Cartesian3(0.0, -4790000.0, 3930000.0));

// 2. Using a HeadingPitchRange offset
const transform = Cesium.Transforms.eastNorthUpToFixedFrame(Cesium.Cartesian3.fromDegrees(-72.0, 40.0));
const heading = Cesium.Math.toRadians(50.0);
const pitch = Cesium.Math.toRadians(-20.0);
const range = 5000.0;
viewer.camera.lookAtTransform(transform, new Cesium.HeadingPitchRange(heading, pitch, range));</code></pre>

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="lookDown">
        <a href="#lookDown" class="doc-link"></a>
        lookDown<span class="signature">(<span class="optional">amount</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1943">engine/Source/Scene/Camera.js 1943</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Rotates the camera around its right vector by amount, in radians, in the opposite direction
of its up vector if not in 2D mode.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>amount</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The amount, in radians, to rotate by. Defaults to <code>defaultLookAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#lookUp">Camera#lookUp</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="lookLeft">
        <a href="#lookLeft" class="doc-link"></a>
        lookLeft<span class="signature">(<span class="optional">amount</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1892">engine/Source/Scene/Camera.js 1892</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Rotates the camera around its up vector by amount, in radians, in the opposite direction
of its right vector if not in 2D mode.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>amount</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The amount, in radians, to rotate by. Defaults to <code>defaultLookAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#lookRight">Camera#lookRight</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="lookRight">
        <a href="#lookRight" class="doc-link"></a>
        lookRight<span class="signature">(<span class="optional">amount</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1909">engine/Source/Scene/Camera.js 1909</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Rotates the camera around its up vector by amount, in radians, in the direction
of its right vector if not in 2D mode.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>amount</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The amount, in radians, to rotate by. Defaults to <code>defaultLookAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#lookLeft">Camera#lookLeft</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="lookUp">
        <a href="#lookUp" class="doc-link"></a>
        lookUp<span class="signature">(<span class="optional">amount</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1926">engine/Source/Scene/Camera.js 1926</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Rotates the camera around its right vector by amount, in radians, in the direction
of its up vector if not in 2D mode.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>amount</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The amount, in radians, to rotate by. Defaults to <code>defaultLookAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#lookDown">Camera#lookDown</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="move">
        <a href="#move" class="doc-link"></a>
        move<span class="signature">(direction, <span class="optional">amount</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1776">engine/Source/Scene/Camera.js 1776</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Translates the camera's position by <code>amount</code> along <code>direction</code>.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>direction</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The direction to move.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>amount</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The amount, in meters, to move. Defaults to <code>defaultMoveAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#moveBackward">Camera#moveBackward</a></li>
    
        <li><a href="Camera.html#moveForward">Camera#moveForward</a></li>
    
        <li><a href="Camera.html#moveLeft">Camera#moveLeft</a></li>
    
        <li><a href="Camera.html#moveRight">Camera#moveRight</a></li>
    
        <li><a href="Camera.html#moveUp">Camera#moveUp</a></li>
    
        <li><a href="Camera.html#moveDown">Camera#moveDown</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="moveBackward">
        <a href="#moveBackward" class="doc-link"></a>
        moveBackward<span class="signature">(<span class="optional">amount</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1822">engine/Source/Scene/Camera.js 1822</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Translates the camera's position by <code>amount</code> along the opposite direction
of the camera's view vector.
When in 2D mode, this will zoom out the camera instead of translating the camera's position.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>amount</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The amount, in meters, to move. Defaults to <code>defaultMoveAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#moveForward">Camera#moveForward</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="moveDown">
        <a href="#moveDown" class="doc-link"></a>
        moveDown<span class="signature">(<span class="optional">amount</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1854">engine/Source/Scene/Camera.js 1854</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Translates the camera's position by <code>amount</code> along the opposite direction
of the camera's up vector.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>amount</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The amount, in meters, to move. Defaults to <code>defaultMoveAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#moveUp">Camera#moveUp</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="moveForward">
        <a href="#moveForward" class="doc-link"></a>
        moveForward<span class="signature">(<span class="optional">amount</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1801">engine/Source/Scene/Camera.js 1801</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Translates the camera's position by <code>amount</code> along the camera's view vector.
When in 2D mode, this will zoom in the camera instead of translating the camera's position.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>amount</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The amount, in meters, to move. Defaults to <code>defaultMoveAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#moveBackward">Camera#moveBackward</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="moveLeft">
        <a href="#moveLeft" class="doc-link"></a>
        moveLeft<span class="signature">(<span class="optional">amount</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1879">engine/Source/Scene/Camera.js 1879</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Translates the camera's position by <code>amount</code> along the opposite direction
of the camera's right vector.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>amount</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The amount, in meters, to move. Defaults to <code>defaultMoveAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#moveRight">Camera#moveRight</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="moveRight">
        <a href="#moveRight" class="doc-link"></a>
        moveRight<span class="signature">(<span class="optional">amount</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1866">engine/Source/Scene/Camera.js 1866</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Translates the camera's position by <code>amount</code> along the camera's right vector.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>amount</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The amount, in meters, to move. Defaults to <code>defaultMoveAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#moveLeft">Camera#moveLeft</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="moveUp">
        <a href="#moveUp" class="doc-link"></a>
        moveUp<span class="signature">(<span class="optional">amount</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1841">engine/Source/Scene/Camera.js 1841</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Translates the camera's position by <code>amount</code> along the camera's up vector.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>amount</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The amount, in meters, to move. Defaults to <code>defaultMoveAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#moveDown">Camera#moveDown</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="pickEllipsoid">
        <a href="#pickEllipsoid" class="doc-link"></a>
        pickEllipsoid<span class="signature">(windowPosition, <span class="optional">ellipsoid</span>, <span class="optional">result</span>)</span> &rarr; <span class="type-signature returnType"><a href="Cartesian3.html">Cartesian3</a>|undefined</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L2903">engine/Source/Scene/Camera.js 2903</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Pick an ellipsoid or map.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>windowPosition</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian2.html">Cartesian2</a></span>


            
            </td>

            
                <td class="default">
                
                </td>
            

            <td class="description last">
            
                
                

                
            
                The x and y coordinates of a pixel.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>ellipsoid</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Ellipsoid.html">Ellipsoid</a></span>


            
            </td>

            
                <td class="default">
                
                    <code class="language-javascript">Ellipsoid.default</code>
                
                </td>
            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The ellipsoid to pick.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>result</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>


            
            </td>

            
                <td class="default">
                
                </td>
            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The object onto which to store the result.</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    If the ellipsoid or map was picked,
returns the point on the surface of the ellipsoid or map in world
coordinates. If the ellipsoid or map was not picked, returns undefined.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">const canvas = viewer.scene.canvas;
const center = new Cesium.Cartesian2(canvas.clientWidth / 2.0, canvas.clientHeight / 2.0);
const ellipsoid = viewer.scene.ellipsoid;
const result = viewer.camera.pickEllipsoid(center, ellipsoid);</code></pre>

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="rotate">
        <a href="#rotate" class="doc-link"></a>
        rotate<span class="signature">(axis, <span class="optional">angle</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L2027">engine/Source/Scene/Camera.js 2027</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Rotates the camera around <code>axis</code> by <code>angle</code>. The distance
of the camera's position to the center of the camera's reference frame remains the same.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>axis</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The axis to rotate around given in world coordinates.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>angle</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The angle, in radians, to rotate by. Defaults to <code>defaultRotateAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#rotateUp">Camera#rotateUp</a></li>
    
        <li><a href="Camera.html#rotateDown">Camera#rotateDown</a></li>
    
        <li><a href="Camera.html#rotateLeft">Camera#rotateLeft</a></li>
    
        <li><a href="Camera.html#rotateRight">Camera#rotateRight</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="rotateDown">
        <a href="#rotateDown" class="doc-link"></a>
        rotateDown<span class="signature">(<span class="optional">angle</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L2058">engine/Source/Scene/Camera.js 2058</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Rotates the camera around the center of the camera's reference frame by angle downwards.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>angle</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The angle, in radians, to rotate by. Defaults to <code>defaultRotateAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#rotateUp">Camera#rotateUp</a></li>
    
        <li><a href="Camera.html#rotate">Camera#rotate</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="rotateLeft">
        <a href="#rotateLeft" class="doc-link"></a>
        rotateLeft<span class="signature">(<span class="optional">angle</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L2157">engine/Source/Scene/Camera.js 2157</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Rotates the camera around the center of the camera's reference frame by angle to the left.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>angle</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The angle, in radians, to rotate by. Defaults to <code>defaultRotateAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#rotateRight">Camera#rotateRight</a></li>
    
        <li><a href="Camera.html#rotate">Camera#rotate</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="rotateRight">
        <a href="#rotateRight" class="doc-link"></a>
        rotateRight<span class="signature">(<span class="optional">angle</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L2144">engine/Source/Scene/Camera.js 2144</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Rotates the camera around the center of the camera's reference frame by angle to the right.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>angle</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The angle, in radians, to rotate by. Defaults to <code>defaultRotateAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#rotateLeft">Camera#rotateLeft</a></li>
    
        <li><a href="Camera.html#rotate">Camera#rotate</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="rotateUp">
        <a href="#rotateUp" class="doc-link"></a>
        rotateUp<span class="signature">(<span class="optional">angle</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L2071">engine/Source/Scene/Camera.js 2071</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Rotates the camera around the center of the camera's reference frame by angle upwards.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>angle</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The angle, in radians, to rotate by. Defaults to <code>defaultRotateAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#rotateDown">Camera#rotateDown</a></li>
    
        <li><a href="Camera.html#rotate">Camera#rotate</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="setView">
        <a href="#setView" class="doc-link"></a>
        setView<span class="signature">(options)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1487">engine/Source/Scene/Camera.js 1487</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Sets the camera position, orientation and transform.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            

            <td class="description last">
            
                Object with the following properties:
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>destination</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>
|

<span class="param-type"><a href="Rectangle.html">Rectangle</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The final position of the camera in world coordinates or a rectangle that would be visible from a top-down view.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>orientation</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#HeadingPitchRollValues">HeadingPitchRollValues</a></span>
|

<span class="param-type"><a href="global.html#DirectionUp">DirectionUp</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                An object that contains either direction and up properties or heading, pitch and roll properties. By default, the direction will point
towards the center of the frame in 3D and in the negative z direction in Columbus view. The up direction will point towards local north in 3D and in the positive
y direction in Columbus view. Orientation is not used in 2D when in infinite scrolling mode.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>endTransform</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Matrix4.html">Matrix4</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                Transform matrix representing the reference frame of the camera.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>convert</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                Whether to convert the destination from world coordinates to scene coordinates (only relevant when not using 3D). Defaults to <code>true</code>.</td>
        </tr>

    
    </tbody>
</table>
            </td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">// 1. Set position with a top-down view
viewer.camera.setView({
    destination : Cesium.Cartesian3.fromDegrees(-117.16, 32.71, 15000.0)
});

// 2 Set view with heading, pitch and roll
viewer.camera.setView({
    destination : cartesianPosition,
    orientation: {
        heading : Cesium.Math.toRadians(90.0), // east, default value is 0.0 (north)
        pitch : Cesium.Math.toRadians(-90),    // default value (looking down)
        roll : 0.0                             // default value
    }
});

// 3. Change heading, pitch and roll with the camera position remaining the same.
viewer.camera.setView({
    orientation: {
        heading : Cesium.Math.toRadians(90.0), // east, default value is 0.0 (north)
        pitch : Cesium.Math.toRadians(-90),    // default value (looking down)
        roll : 0.0                             // default value
    }
});


// 4. View rectangle with a top-down view
viewer.camera.setView({
    destination : Cesium.Rectangle.fromDegrees(west, south, east, north)
});

// 5. Set position with an orientation using unit vectors.
viewer.camera.setView({
    destination : Cesium.Cartesian3.fromDegrees(-122.19, 46.25, 5000.0),
    orientation : {
        direction : new Cesium.Cartesian3(-0.04231243104240401, -0.20123236049443421, -0.97862924300734),
        up : new Cesium.Cartesian3(-0.47934589305293746, -0.8553216253114552, 0.1966022179118339)
    }
});</code></pre>

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="switchToOrthographicFrustum">
        <a href="#switchToOrthographicFrustum" class="doc-link"></a>
        switchToOrthographicFrustum<span class="signature">()</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L3931">engine/Source/Scene/Camera.js 3931</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Switches the frustum/projection to orthographic.

This function is a no-op in 2D which will always be orthographic.
</div>

























<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="switchToPerspectiveFrustum">
        <a href="#switchToPerspectiveFrustum" class="doc-link"></a>
        switchToPerspectiveFrustum<span class="signature">()</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L3911">engine/Source/Scene/Camera.js 3911</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Switches the frustum/projection to perspective.

This function is a no-op in 2D which must always be orthographic.
</div>

























<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="twistLeft">
        <a href="#twistLeft" class="doc-link"></a>
        twistLeft<span class="signature">(<span class="optional">amount</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1996">engine/Source/Scene/Camera.js 1996</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Rotate the camera counter-clockwise around its direction vector by amount, in radians.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>amount</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The amount, in radians, to rotate by. Defaults to <code>defaultLookAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#twistRight">Camera#twistRight</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="twistRight">
        <a href="#twistRight" class="doc-link"></a>
        twistRight<span class="signature">(<span class="optional">amount</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L2008">engine/Source/Scene/Camera.js 2008</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Rotate the camera clockwise around its direction vector by amount, in radians.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>amount</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The amount, in radians, to rotate by. Defaults to <code>defaultLookAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#twistLeft">Camera#twistLeft</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="viewBoundingSphere">
        <a href="#viewBoundingSphere" class="doc-link"></a>
        viewBoundingSphere<span class="signature">(boundingSphere, <span class="optional">offset</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L3533">engine/Source/Scene/Camera.js 3533</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Sets the camera so that the current view contains the provided bounding sphere.

<p>The offset is heading/pitch/range in the local east-north-up reference frame centered at the center of the bounding sphere.
The heading and the pitch angles are defined in the local east-north-up reference frame.
The heading is the angle from y axis and increasing towards the x axis. Pitch is the rotation from the xy-plane. Positive pitch
angles are below the plane. Negative pitch angles are above the plane. The range is the distance from the center. If the range is
zero, a range will be computed such that the whole bounding sphere is visible.</p>

<p>In 2D, there must be a top down view. The camera will be placed above the target looking down. The height above the
target will be the range. The heading will be determined from the offset. If the heading cannot be
determined from the offset, the heading will be north.</p>
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>boundingSphere</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="BoundingSphere.html">BoundingSphere</a></span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The bounding sphere to view, in world coordinates.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>offset</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="HeadingPitchRange.html">HeadingPitchRange</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The offset from the target in the local east-north-up reference frame centered at the target.</td>
        </tr>

    
    </tbody>
</table>













<h5>Throws:</h5>
<ul>
        <li>

    <div class="param-desc">
        
<span class="param-type"><a href="DeveloperError.html">DeveloperError</a></span>

: viewBoundingSphere is not supported while morphing.
    </div>

</li>
    </ul>



<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="worldToCameraCoordinates">
        <a href="#worldToCameraCoordinates" class="doc-link"></a>
        worldToCameraCoordinates<span class="signature">(cartesian, <span class="optional">result</span>)</span> &rarr; <span class="type-signature returnType"><a href="Cartesian4.html">Cartesian4</a></span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1605">engine/Source/Scene/Camera.js 1605</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Transform a vector or point from world coordinates to the camera's reference frame.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>cartesian</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian4.html">Cartesian4</a></span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The vector or point to transform.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>result</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian4.html">Cartesian4</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The object onto which to store the result.</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    The transformed vector or point.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="worldToCameraCoordinatesPoint">
        <a href="#worldToCameraCoordinatesPoint" class="doc-link"></a>
        worldToCameraCoordinatesPoint<span class="signature">(cartesian, <span class="optional">result</span>)</span> &rarr; <span class="type-signature returnType"><a href="Cartesian3.html">Cartesian3</a></span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1626">engine/Source/Scene/Camera.js 1626</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Transform a point from world coordinates to the camera's reference frame.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>cartesian</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The point to transform.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>result</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The object onto which to store the result.</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    The transformed point.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="worldToCameraCoordinatesVector">
        <a href="#worldToCameraCoordinatesVector" class="doc-link"></a>
        worldToCameraCoordinatesVector<span class="signature">(cartesian, <span class="optional">result</span>)</span> &rarr; <span class="type-signature returnType"><a href="Cartesian3.html">Cartesian3</a></span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L1647">engine/Source/Scene/Camera.js 1647</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Transform a vector from world coordinates to the camera's reference frame.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>cartesian</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The vector to transform.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>result</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The object onto which to store the result.</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    The transformed vector.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="zoomIn">
        <a href="#zoomIn" class="doc-link"></a>
        zoomIn<span class="signature">(<span class="optional">amount</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L2254">engine/Source/Scene/Camera.js 2254</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Zooms <code>amount</code> along the camera's view vector.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>amount</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The amount to move. Defaults to <code>defaultZoomAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#zoomOut">Camera#zoomOut</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="zoomOut">
        <a href="#zoomOut" class="doc-link"></a>
        zoomOut<span class="signature">(<span class="optional">amount</span>)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L2271">engine/Source/Scene/Camera.js 2271</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Zooms <code>amount</code> along the opposite direction of
the camera's view vector.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>amount</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                The amount to move. Defaults to <code>defaultZoomAmount</code>.</td>
        </tr>

    
    </tbody>
</table>















<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Camera.html#zoomIn">Camera#zoomIn</a></li>
    </ul>
    

    
</dl>


        
    

    
        <h3 class="subsection-title">Type Definitions</h3>

        
                
    <div class="nameContainer">
    <h4 class="name" id=".FlightCancelledCallback">
        <a href="#.FlightCancelledCallback" class="doc-link"></a>
        Cesium.Camera.FlightCancelledCallback<span class="signature">()</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L3974">engine/Source/Scene/Camera.js 3974</a>
</div>


    </h4>

    </div>

    


<div class="description">
    A function that will execute when a flight is cancelled.
</div>

























<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


            
                
    <div class="nameContainer">
    <h4 class="name" id=".FlightCompleteCallback">
        <a href="#.FlightCompleteCallback" class="doc-link"></a>
        Cesium.Camera.FlightCompleteCallback<span class="signature">()</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Camera.js#L3969">engine/Source/Scene/Camera.js 3969</a>
</div>


    </h4>

    </div>

    


<div class="description">
    A function that will execute when a flight completes.
</div>

























<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


            
    

    
</article>

</section>





    <div class="help">
        Need help? The fastest way to get answers is from the community and team on the <a href="https://community.cesium.com/">Cesium Forum</a>.
    </div>

    <footer>
        Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a>
    </footer>
</div>

<div class="nav">
    <div class="menu">
        <div class="search-wrapper">
            <input type="text" class="classFilter" id="ClassFilter" placeholder="Search">
            <div class="shortcut"><kbd>Ctrl</kbd><kbd>K</kbd></div>
        </div>
        <div id="ClassList"><h5>packages/engine</h5><ul><li data-name="AnchorPointDirect"><a href="AnchorPointDirect.html">AnchorPointDirect</a></li><li data-name="AnchorPointIndirect"><a href="AnchorPointIndirect.html">AnchorPointIndirect</a></li><li data-name="Appearance"><a href="Appearance.html">Appearance</a></li><li data-name="ArcGisBaseMapType"><a href="global.html#ArcGisBaseMapType">ArcGisBaseMapType</a></li><li data-name="ArcGisMapServerImageryProvider"><a href="ArcGisMapServerImageryProvider.html">ArcGisMapServerImageryProvider</a></li><li data-name="ArcGisMapService"><a href="ArcGisMapService.html">ArcGisMapService</a></li><li data-name="ArcGISTiledElevationTerrainProvider"><a href="ArcGISTiledElevationTerrainProvider.html">ArcGISTiledElevationTerrainProvider</a></li><li data-name="ArcType"><a href="global.html#ArcType">ArcType</a></li><li data-name="AssociativeArray"><a href="AssociativeArray.html">AssociativeArray</a></li><li data-name="Atmosphere"><a href="Atmosphere.html">Atmosphere</a></li><li data-name="availableLevels"><a href="global.html#availableLevels">availableLevels</a></li><li data-name="Axis"><a href="global.html#Axis">Axis</a></li><li data-name="AxisAlignedBoundingBox"><a href="AxisAlignedBoundingBox.html">AxisAlignedBoundingBox</a></li><li data-name="barycentricCoordinates"><a href="global.html#barycentricCoordinates">barycentricCoordinates</a></li><li data-name="Billboard"><a href="Billboard.html">Billboard</a></li><li data-name="BillboardCollection"><a href="BillboardCollection.html">BillboardCollection</a></li><li data-name="BillboardGraphics"><a href="BillboardGraphics.html">BillboardGraphics</a></li><li data-name="BillboardVisualizer"><a href="BillboardVisualizer.html">BillboardVisualizer</a></li><li data-name="binarySearch"><a href="global.html#binarySearch">binarySearch</a></li><li data-name="binarySearchComparator"><a href="global.html#binarySearchComparator">binarySearchComparator</a></li><li data-name="BingMapsGeocoderService"><a href="BingMapsGeocoderService.html">BingMapsGeocoderService</a></li><li data-name="BingMapsImageryProvider"><a href="BingMapsImageryProvider.html">BingMapsImageryProvider</a></li><li data-name="BingMapsStyle"><a href="global.html#BingMapsStyle">BingMapsStyle</a></li><li data-name="BlendEquation"><a href="global.html#BlendEquation">BlendEquation</a></li><li data-name="BlendFunction"><a href="global.html#BlendFunction">BlendFunction</a></li><li data-name="BlendingState"><a href="BlendingState.html">BlendingState</a></li><li data-name="BlendOption"><a href="global.html#BlendOption">BlendOption</a></li><li data-name="BoundingRectangle"><a href="BoundingRectangle.html">BoundingRectangle</a></li><li data-name="BoundingSphere"><a href="BoundingSphere.html">BoundingSphere</a></li><li data-name="BoxEmitter"><a href="BoxEmitter.html">BoxEmitter</a></li><li data-name="BoxGeometry"><a href="BoxGeometry.html">BoxGeometry</a></li><li data-name="BoxGeometryUpdater"><a href="BoxGeometryUpdater.html">BoxGeometryUpdater</a></li><li data-name="BoxGraphics"><a href="BoxGraphics.html">BoxGraphics</a></li><li data-name="BoxOutlineGeometry"><a href="BoxOutlineGeometry.html">BoxOutlineGeometry</a></li><li data-name="buildModuleUrl"><a href="global.html#buildModuleUrl">buildModuleUrl</a></li><li data-name="CallbackPositionProperty"><a href="CallbackPositionProperty.html">CallbackPositionProperty</a></li><li data-name="CallbackProperty"><a href="CallbackProperty.html">CallbackProperty</a></li><li data-name="Camera"><a href="Camera.html">Camera</a></li><li data-name="CameraEventAggregator"><a href="CameraEventAggregator.html">CameraEventAggregator</a></li><li data-name="CameraEventType"><a href="global.html#CameraEventType">CameraEventType</a></li><li data-name="Cartesian2"><a href="Cartesian2.html">Cartesian2</a></li><li data-name="Cartesian3"><a href="Cartesian3.html">Cartesian3</a></li><li data-name="Cartesian4"><a href="Cartesian4.html">Cartesian4</a></li><li data-name="Cartographic"><a href="Cartographic.html">Cartographic</a></li><li data-name="CartographicGeocoderService"><a href="CartographicGeocoderService.html">CartographicGeocoderService</a></li><li data-name="CatmullRomSpline"><a href="CatmullRomSpline.html">CatmullRomSpline</a></li><li data-name="Cesium3DTile"><a href="Cesium3DTile.html">Cesium3DTile</a></li><li data-name="Cesium3DTileColorBlendMode"><a href="global.html#Cesium3DTileColorBlendMode">Cesium3DTileColorBlendMode</a></li><li data-name="Cesium3DTileContent"><a href="Cesium3DTileContent.html">Cesium3DTileContent</a></li><li data-name="Cesium3DTileFeature"><a href="Cesium3DTileFeature.html">Cesium3DTileFeature</a></li><li data-name="Cesium3DTilePointFeature"><a href="Cesium3DTilePointFeature.html">Cesium3DTilePointFeature</a></li><li data-name="Cesium3DTileset"><a href="Cesium3DTileset.html">Cesium3DTileset</a></li><li data-name="Cesium3DTilesetGraphics"><a href="Cesium3DTilesetGraphics.html">Cesium3DTilesetGraphics</a></li><li data-name="Cesium3DTilesetVisualizer"><a href="Cesium3DTilesetVisualizer.html">Cesium3DTilesetVisualizer</a></li><li data-name="Cesium3DTileStyle"><a href="Cesium3DTileStyle.html">Cesium3DTileStyle</a></li><li data-name="Cesium3DTilesVoxelProvider"><a href="Cesium3DTilesVoxelProvider.html">Cesium3DTilesVoxelProvider</a></li><li data-name="CesiumTerrainProvider"><a href="CesiumTerrainProvider.html">CesiumTerrainProvider</a></li><li data-name="CesiumWidget"><a href="CesiumWidget.html">CesiumWidget</a></li><li data-name="Check"><a href="global.html#Check">Check</a></li><li data-name="CheckerboardMaterialProperty"><a href="CheckerboardMaterialProperty.html">CheckerboardMaterialProperty</a></li><li data-name="CircleEmitter"><a href="CircleEmitter.html">CircleEmitter</a></li><li data-name="CircleGeometry"><a href="CircleGeometry.html">CircleGeometry</a></li><li data-name="CircleOutlineGeometry"><a href="CircleOutlineGeometry.html">CircleOutlineGeometry</a></li><li data-name="ClassificationPrimitive"><a href="ClassificationPrimitive.html">ClassificationPrimitive</a></li><li data-name="ClassificationType"><a href="global.html#ClassificationType">ClassificationType</a></li><li data-name="className"><a href="global.html#className">className</a></li><li data-name="classProperty"><a href="global.html#classProperty">classProperty</a></li><li data-name="ClippingPlane"><a href="ClippingPlane.html">ClippingPlane</a></li><li data-name="ClippingPlaneCollection"><a href="ClippingPlaneCollection.html">ClippingPlaneCollection</a></li><li data-name="ClippingPolygon"><a href="ClippingPolygon.html">ClippingPolygon</a></li><li data-name="ClippingPolygonCollection"><a href="ClippingPolygonCollection.html">ClippingPolygonCollection</a></li><li data-name="Clock"><a href="Clock.html">Clock</a></li><li data-name="ClockRange"><a href="global.html#ClockRange">ClockRange</a></li><li data-name="ClockStep"><a href="global.html#ClockStep">ClockStep</a></li><li data-name="clone"><a href="global.html#clone">clone</a></li><li data-name="CloudCollection"><a href="CloudCollection.html">CloudCollection</a></li><li data-name="CloudType"><a href="global.html#CloudType">CloudType</a></li><li data-name="Color"><a href="Color.html">Color</a></li><li data-name="ColorBlendMode"><a href="global.html#ColorBlendMode">ColorBlendMode</a></li><li data-name="ColorGeometryInstanceAttribute"><a href="ColorGeometryInstanceAttribute.html">ColorGeometryInstanceAttribute</a></li><li data-name="ColorMaterialProperty"><a href="ColorMaterialProperty.html">ColorMaterialProperty</a></li><li data-name="combine"><a href="global.html#combine">combine</a></li><li data-name="ComponentDatatype"><a href="global.html#ComponentDatatype">ComponentDatatype</a></li><li data-name="ComponentReaderCallback"><a href="global.html#ComponentReaderCallback">ComponentReaderCallback</a></li><li data-name="ComponentsReaderCallback"><a href="global.html#ComponentsReaderCallback">ComponentsReaderCallback</a></li><li data-name="CompositeEntityCollection"><a href="CompositeEntityCollection.html">CompositeEntityCollection</a></li><li data-name="CompositeMaterialProperty"><a href="CompositeMaterialProperty.html">CompositeMaterialProperty</a></li><li data-name="CompositePositionProperty"><a href="CompositePositionProperty.html">CompositePositionProperty</a></li><li data-name="CompositeProperty"><a href="CompositeProperty.html">CompositeProperty</a></li><li data-name="CompressedTextureBuffer"><a href="CompressedTextureBuffer.html">CompressedTextureBuffer</a></li><li data-name="computePickingDrawingBufferRectangle"><a href="global.html#computePickingDrawingBufferRectangle">computePickingDrawingBufferRectangle</a></li><li data-name="ConditionsExpression"><a href="ConditionsExpression.html">ConditionsExpression</a></li><li data-name="ConeEmitter"><a href="ConeEmitter.html">ConeEmitter</a></li><li data-name="ConstantPositionProperty"><a href="ConstantPositionProperty.html">ConstantPositionProperty</a></li><li data-name="ConstantProperty"><a href="ConstantProperty.html">ConstantProperty</a></li><li data-name="ConstantSpline"><a href="ConstantSpline.html">ConstantSpline</a></li><li data-name="ContextOptions"><a href="global.html#ContextOptions">ContextOptions</a></li><li data-name="CoplanarPolygonGeometry"><a href="CoplanarPolygonGeometry.html">CoplanarPolygonGeometry</a></li><li data-name="CoplanarPolygonOutlineGeometry"><a href="CoplanarPolygonOutlineGeometry.html">CoplanarPolygonOutlineGeometry</a></li><li data-name="CornerType"><a href="global.html#CornerType">CornerType</a></li><li data-name="CorrelationGroup"><a href="CorrelationGroup.html">CorrelationGroup</a></li><li data-name="CorridorGeometry"><a href="CorridorGeometry.html">CorridorGeometry</a></li><li data-name="CorridorGeometryUpdater"><a href="CorridorGeometryUpdater.html">CorridorGeometryUpdater</a></li><li data-name="CorridorGraphics"><a href="CorridorGraphics.html">CorridorGraphics</a></li><li data-name="CorridorOutlineGeometry"><a href="CorridorOutlineGeometry.html">CorridorOutlineGeometry</a></li><li data-name="createAnchorPointDirect"><a href="global.html#createAnchorPointDirect">createAnchorPointDirect</a></li><li data-name="createAnchorPointIndirect"><a href="global.html#createAnchorPointIndirect">createAnchorPointIndirect</a></li><li data-name="createCorrelationGroup"><a href="global.html#createCorrelationGroup">createCorrelationGroup</a></li><li data-name="createCovarianceMatrixFromUpperTriangle"><a href="global.html#createCovarianceMatrixFromUpperTriangle">createCovarianceMatrixFromUpperTriangle</a></li><li data-name="createElevationBandMaterial"><a href="global.html#createElevationBandMaterial">createElevationBandMaterial</a></li><li data-name="createElevationBandMaterialBand"><a href="global.html#createElevationBandMaterialBand">createElevationBandMaterialBand</a></li><li data-name="createElevationBandMaterialEntry"><a href="global.html#createElevationBandMaterialEntry">createElevationBandMaterialEntry</a></li><li data-name="createGooglePhotorealistic3DTileset"><a href="global.html#createGooglePhotorealistic3DTileset">createGooglePhotorealistic3DTileset</a></li><li data-name="createGuid"><a href="global.html#createGuid">createGuid</a></li><li data-name="createOsmBuildingsAsync"><a href="global.html#createOsmBuildingsAsync">createOsmBuildingsAsync</a></li><li data-name="createTangentSpaceDebugPrimitive"><a href="global.html#createTangentSpaceDebugPrimitive">createTangentSpaceDebugPrimitive</a></li><li data-name="createWorldBathymetryAsync"><a href="global.html#createWorldBathymetryAsync">createWorldBathymetryAsync</a></li><li data-name="createWorldImageryAsync"><a href="global.html#createWorldImageryAsync">createWorldImageryAsync</a></li><li data-name="createWorldTerrainAsync"><a href="global.html#createWorldTerrainAsync">createWorldTerrainAsync</a></li><li data-name="Credit"><a href="Credit.html">Credit</a></li><li data-name="CreditDisplay"><a href="CreditDisplay.html">CreditDisplay</a></li><li data-name="CubicRealPolynomial"><a href="CubicRealPolynomial.html">CubicRealPolynomial</a></li><li data-name="CullFace"><a href="global.html#CullFace">CullFace</a></li><li data-name="CullingVolume"><a href="CullingVolume.html">CullingVolume</a></li><li data-name="CumulusCloud"><a href="CumulusCloud.html">CumulusCloud</a></li><li data-name="CustomDataSource"><a href="CustomDataSource.html">CustomDataSource</a></li><li data-name="CustomHeightmapTerrainProvider"><a href="CustomHeightmapTerrainProvider.html">CustomHeightmapTerrainProvider</a></li><li data-name="CustomShader"><a href="CustomShader.html">CustomShader</a></li><li data-name="CustomShaderMode"><a href="global.html#CustomShaderMode">CustomShaderMode</a></li><li data-name="CustomShaderTranslucencyMode"><a href="global.html#CustomShaderTranslucencyMode">CustomShaderTranslucencyMode</a></li><li data-name="CylinderGeometry"><a href="CylinderGeometry.html">CylinderGeometry</a></li><li data-name="CylinderGeometryUpdater"><a href="CylinderGeometryUpdater.html">CylinderGeometryUpdater</a></li><li data-name="CylinderGraphics"><a href="CylinderGraphics.html">CylinderGraphics</a></li><li data-name="CylinderOutlineGeometry"><a href="CylinderOutlineGeometry.html">CylinderOutlineGeometry</a></li><li data-name="CzmlDataSource"><a href="CzmlDataSource.html">CzmlDataSource</a></li><li data-name="DataSource"><a href="DataSource.html">DataSource</a></li><li data-name="DataSourceClock"><a href="DataSourceClock.html">DataSourceClock</a></li><li data-name="DataSourceCollection"><a href="DataSourceCollection.html">DataSourceCollection</a></li><li data-name="DataSourceDisplay"><a href="DataSourceDisplay.html">DataSourceDisplay</a></li><li data-name="DebugAppearance"><a href="DebugAppearance.html">DebugAppearance</a></li><li data-name="DebugCameraPrimitive"><a href="DebugCameraPrimitive.html">DebugCameraPrimitive</a></li><li data-name="DebugModelMatrixPrimitive"><a href="DebugModelMatrixPrimitive.html">DebugModelMatrixPrimitive</a></li><li data-name="DefaultProxy"><a href="DefaultProxy.html">DefaultProxy</a></li><li data-name="defaultValue"><a href="global.html#defaultValue">defaultValue</a></li><li data-name="defined"><a href="global.html#defined">defined</a></li><li data-name="DepthFunction"><a href="global.html#DepthFunction">DepthFunction</a></li><li data-name="destroyObject"><a href="global.html#destroyObject">destroyObject</a></li><li data-name="DeveloperError"><a href="DeveloperError.html">DeveloperError</a></li><li data-name="DirectionalLight"><a href="DirectionalLight.html">DirectionalLight</a></li><li data-name="DirectionUp"><a href="global.html#DirectionUp">DirectionUp</a></li><li data-name="DiscardEmptyTileImagePolicy"><a href="DiscardEmptyTileImagePolicy.html">DiscardEmptyTileImagePolicy</a></li><li data-name="DiscardMissingTileImagePolicy"><a href="DiscardMissingTileImagePolicy.html">DiscardMissingTileImagePolicy</a></li><li data-name="DistanceDisplayCondition"><a href="DistanceDisplayCondition.html">DistanceDisplayCondition</a></li><li data-name="DistanceDisplayConditionGeometryInstanceAttribute"><a href="DistanceDisplayConditionGeometryInstanceAttribute.html">DistanceDisplayConditionGeometryInstanceAttribute</a></li><li data-name="DONE"><a href="global.html#DONE">DONE</a></li><li data-name="DynamicAtmosphereLightingType"><a href="global.html#DynamicAtmosphereLightingType">DynamicAtmosphereLightingType</a></li><li data-name="DynamicEnvironmentMapManager"><a href="DynamicEnvironmentMapManager.html">DynamicEnvironmentMapManager</a></li><li data-name="EasingFunction"><a href="EasingFunction.html">EasingFunction</a></li><li data-name="EllipseGeometry"><a href="EllipseGeometry.html">EllipseGeometry</a></li><li data-name="EllipseGeometryUpdater"><a href="EllipseGeometryUpdater.html">EllipseGeometryUpdater</a></li><li data-name="EllipseGraphics"><a href="EllipseGraphics.html">EllipseGraphics</a></li><li data-name="EllipseOutlineGeometry"><a href="EllipseOutlineGeometry.html">EllipseOutlineGeometry</a></li><li data-name="Ellipsoid"><a href="Ellipsoid.html">Ellipsoid</a></li><li data-name="EllipsoidGeodesic"><a href="EllipsoidGeodesic.html">EllipsoidGeodesic</a></li><li data-name="EllipsoidGeometry"><a href="EllipsoidGeometry.html">EllipsoidGeometry</a></li><li data-name="EllipsoidGeometryUpdater"><a href="EllipsoidGeometryUpdater.html">EllipsoidGeometryUpdater</a></li><li data-name="EllipsoidGraphics"><a href="EllipsoidGraphics.html">EllipsoidGraphics</a></li><li data-name="EllipsoidOutlineGeometry"><a href="EllipsoidOutlineGeometry.html">EllipsoidOutlineGeometry</a></li><li data-name="EllipsoidRhumbLine"><a href="EllipsoidRhumbLine.html">EllipsoidRhumbLine</a></li><li data-name="EllipsoidSurfaceAppearance"><a href="EllipsoidSurfaceAppearance.html">EllipsoidSurfaceAppearance</a></li><li data-name="EllipsoidTangentPlane"><a href="EllipsoidTangentPlane.html">EllipsoidTangentPlane</a></li><li data-name="EllipsoidTerrainProvider"><a href="EllipsoidTerrainProvider.html">EllipsoidTerrainProvider</a></li><li data-name="Entity"><a href="Entity.html">Entity</a></li><li data-name="EntityCluster"><a href="EntityCluster.html">EntityCluster</a></li><li data-name="EntityCollection"><a href="EntityCollection.html">EntityCollection</a></li><li data-name="EntityView"><a href="EntityView.html">EntityView</a></li><li data-name="Event"><a href="Event.html">Event</a></li><li data-name="EventHelper"><a href="EventHelper.html">EventHelper</a></li><li data-name="excludesReverseAxis"><a href="global.html#excludesReverseAxis">excludesReverseAxis</a></li><li data-name="exportKml"><a href="global.html#exportKml">exportKml</a></li><li data-name="exportKmlModelCallback"><a href="global.html#exportKmlModelCallback">exportKmlModelCallback</a></li><li data-name="exportKmlResultKml"><a href="global.html#exportKmlResultKml">exportKmlResultKml</a></li><li data-name="exportKmlResultKmz"><a href="global.html#exportKmlResultKmz">exportKmlResultKmz</a></li><li data-name="Expression"><a href="Expression.html">Expression</a></li><li data-name="ExtrapolationType"><a href="global.html#ExtrapolationType">ExtrapolationType</a></li><li data-name="FAILED"><a href="global.html#FAILED">FAILED</a></li><li data-name="FeatureDetection"><a href="FeatureDetection.html">FeatureDetection</a></li><li data-name="Fog"><a href="Fog.html">Fog</a></li><li data-name="formatError"><a href="global.html#formatError">formatError</a></li><li data-name="FrameRateMonitor"><a href="FrameRateMonitor.html">FrameRateMonitor</a></li><li data-name="Frozen"><a href="Frozen.html">Frozen</a></li><li data-name="FrustumGeometry"><a href="FrustumGeometry.html">FrustumGeometry</a></li><li data-name="FrustumOutlineGeometry"><a href="FrustumOutlineGeometry.html">FrustumOutlineGeometry</a></li><li data-name="Fullscreen"><a href="Fullscreen.html">Fullscreen</a></li><li data-name="GeocoderService"><a href="GeocoderService.html">GeocoderService</a></li><li data-name="GeocodeType"><a href="global.html#GeocodeType">GeocodeType</a></li><li data-name="GeographicProjection"><a href="GeographicProjection.html">GeographicProjection</a></li><li data-name="GeographicTilingScheme"><a href="GeographicTilingScheme.html">GeographicTilingScheme</a></li><li data-name="GeoJsonDataSource"><a href="GeoJsonDataSource.html">GeoJsonDataSource</a></li><li data-name="Geometry"><a href="Geometry.html">Geometry</a></li><li data-name="GeometryAttribute"><a href="GeometryAttribute.html">GeometryAttribute</a></li><li data-name="GeometryAttributes"><a href="GeometryAttributes.html">GeometryAttributes</a></li><li data-name="GeometryFactory"><a href="GeometryFactory.html">GeometryFactory</a></li><li data-name="GeometryInstance"><a href="GeometryInstance.html">GeometryInstance</a></li><li data-name="GeometryInstanceAttribute"><a href="GeometryInstanceAttribute.html">GeometryInstanceAttribute</a></li><li data-name="GeometryPipeline"><a href="GeometryPipeline.html">GeometryPipeline</a></li><li data-name="GeometryUpdater"><a href="GeometryUpdater.html">GeometryUpdater</a></li><li data-name="geometryUpdaters"><a href="global.html#geometryUpdaters">geometryUpdaters</a></li><li data-name="GeometryVisualizer"><a href="GeometryVisualizer.html">GeometryVisualizer</a></li><li data-name="getAbsoluteUri"><a href="global.html#getAbsoluteUri">getAbsoluteUri</a></li><li data-name="getBaseUri"><a href="global.html#getBaseUri">getBaseUri</a></li><li data-name="getExtensionFromUri"><a href="global.html#getExtensionFromUri">getExtensionFromUri</a></li><li data-name="GetFeatureInfoFormat"><a href="GetFeatureInfoFormat.html">GetFeatureInfoFormat</a></li><li data-name="getFilenameFromUri"><a href="global.html#getFilenameFromUri">getFilenameFromUri</a></li><li data-name="getGlslType"><a href="global.html#getGlslType">getGlslType</a></li><li data-name="getImagePixels"><a href="global.html#getImagePixels">getImagePixels</a></li><li data-name="getSourceValueStringComponent"><a href="global.html#getSourceValueStringComponent">getSourceValueStringComponent</a></li><li data-name="getSourceValueStringScalar"><a href="global.html#getSourceValueStringScalar">getSourceValueStringScalar</a></li><li data-name="getTimestamp"><a href="global.html#getTimestamp">getTimestamp</a></li><li data-name="Globe"><a href="Globe.html">Globe</a></li><li data-name="GlobeTranslucency"><a href="GlobeTranslucency.html">GlobeTranslucency</a></li><li data-name="GltfGpmLocal"><a href="GltfGpmLocal.html">GltfGpmLocal</a></li><li data-name="GoogleEarthEnterpriseImageryProvider"><a href="GoogleEarthEnterpriseImageryProvider.html">GoogleEarthEnterpriseImageryProvider</a></li><li data-name="GoogleEarthEnterpriseMapsProvider"><a href="GoogleEarthEnterpriseMapsProvider.html">GoogleEarthEnterpriseMapsProvider</a></li><li data-name="GoogleEarthEnterpriseMetadata"><a href="GoogleEarthEnterpriseMetadata.html">GoogleEarthEnterpriseMetadata</a></li><li data-name="GoogleEarthEnterpriseTerrainData"><a href="GoogleEarthEnterpriseTerrainData.html">GoogleEarthEnterpriseTerrainData</a></li><li data-name="GoogleEarthEnterpriseTerrainProvider"><a href="GoogleEarthEnterpriseTerrainProvider.html">GoogleEarthEnterpriseTerrainProvider</a></li><li data-name="GoogleGeocoderService"><a href="GoogleGeocoderService.html">GoogleGeocoderService</a></li><li data-name="GoogleMaps"><a href="GoogleMaps.html">GoogleMaps</a></li><li data-name="GpxDataSource"><a href="GpxDataSource.html">GpxDataSource</a></li><li data-name="GregorianDate"><a href="GregorianDate.html">GregorianDate</a></li><li data-name="GridImageryProvider"><a href="GridImageryProvider.html">GridImageryProvider</a></li><li data-name="GridMaterialProperty"><a href="GridMaterialProperty.html">GridMaterialProperty</a></li><li data-name="GroundGeometryUpdater"><a href="GroundGeometryUpdater.html">GroundGeometryUpdater</a></li><li data-name="GroundPolylineGeometry"><a href="GroundPolylineGeometry.html">GroundPolylineGeometry</a></li><li data-name="GroundPolylinePrimitive"><a href="GroundPolylinePrimitive.html">GroundPolylinePrimitive</a></li><li data-name="GroundPrimitive"><a href="GroundPrimitive.html">GroundPrimitive</a></li><li data-name="HeadingPitchRange"><a href="HeadingPitchRange.html">HeadingPitchRange</a></li><li data-name="HeadingPitchRoll"><a href="HeadingPitchRoll.html">HeadingPitchRoll</a></li><li data-name="HeadingPitchRollValues"><a href="global.html#HeadingPitchRollValues">HeadingPitchRollValues</a></li><li data-name="HeightmapEncoding"><a href="global.html#HeightmapEncoding">HeightmapEncoding</a></li><li data-name="HeightmapTerrainData"><a href="HeightmapTerrainData.html">HeightmapTerrainData</a></li><li data-name="HeightReference"><a href="global.html#HeightReference">HeightReference</a></li><li data-name="HermitePolynomialApproximation"><a href="HermitePolynomialApproximation.html">HermitePolynomialApproximation</a></li><li data-name="HermiteSpline"><a href="HermiteSpline.html">HermiteSpline</a></li><li data-name="HilbertOrder"><a href="HilbertOrder.html">HilbertOrder</a></li><li data-name="HorizontalOrigin"><a href="global.html#HorizontalOrigin">HorizontalOrigin</a></li><li data-name="I3SDataProvider"><a href="I3SDataProvider.html">I3SDataProvider</a></li><li data-name="I3SFeature"><a href="I3SFeature.html">I3SFeature</a></li><li data-name="I3SField"><a href="I3SField.html">I3SField</a></li><li data-name="I3SGeometry"><a href="I3SGeometry.html">I3SGeometry</a></li><li data-name="I3SLayer"><a href="I3SLayer.html">I3SLayer</a></li><li data-name="I3SNode"><a href="I3SNode.html">I3SNode</a></li><li data-name="I3SStatistics"><a href="I3SStatistics.html">I3SStatistics</a></li><li data-name="I3SSublayer"><a href="I3SSublayer.html">I3SSublayer</a></li><li data-name="I3SSymbology"><a href="I3SSymbology.html">I3SSymbology</a></li><li data-name="ImageBasedLighting"><a href="ImageBasedLighting.html">ImageBasedLighting</a></li><li data-name="ImageMaterialProperty"><a href="ImageMaterialProperty.html">ImageMaterialProperty</a></li><li data-name="ImageryLayer"><a href="ImageryLayer.html">ImageryLayer</a></li><li data-name="ImageryLayerCollection"><a href="ImageryLayerCollection.html">ImageryLayerCollection</a></li><li data-name="ImageryLayerFeatureInfo"><a href="ImageryLayerFeatureInfo.html">ImageryLayerFeatureInfo</a></li><li data-name="ImageryProvider"><a href="ImageryProvider.html">ImageryProvider</a></li><li data-name="ImageryTypes"><a href="global.html#ImageryTypes">ImageryTypes</a></li><li data-name="includesReverseAxis"><a href="global.html#includesReverseAxis">includesReverseAxis</a></li><li data-name="IndexDatatype"><a href="global.html#IndexDatatype">IndexDatatype</a></li><li data-name="Intersect"><a href="global.html#Intersect">Intersect</a></li><li data-name="Intersections2D"><a href="Intersections2D.html">Intersections2D</a></li><li data-name="IntersectionTests"><a href="IntersectionTests.html">IntersectionTests</a></li><li data-name="Interval"><a href="Interval.html">Interval</a></li><li data-name="Ion"><a href="Ion.html">Ion</a></li><li data-name="IonGeocodeProviderType"><a href="global.html#IonGeocodeProviderType">IonGeocodeProviderType</a></li><li data-name="IonGeocoderService"><a href="IonGeocoderService.html">IonGeocoderService</a></li><li data-name="IonImageryProvider"><a href="IonImageryProvider.html">IonImageryProvider</a></li><li data-name="IonResource"><a href="IonResource.html">IonResource</a></li><li data-name="IonWorldImageryStyle"><a href="global.html#IonWorldImageryStyle">IonWorldImageryStyle</a></li><li data-name="isLeapYear"><a href="global.html#isLeapYear">isLeapYear</a></li><li data-name="Iso8601"><a href="Iso8601.html">Iso8601</a></li><li data-name="ITwinData"><a href="ITwinData.html">ITwinData</a></li><li data-name="ITwinPlatform"><a href="ITwinPlatform.html">ITwinPlatform</a></li><li data-name="JulianDate"><a href="JulianDate.html">JulianDate</a></li><li data-name="KeyboardEventModifier"><a href="global.html#KeyboardEventModifier">KeyboardEventModifier</a></li><li data-name="KmlCamera"><a href="KmlCamera.html">KmlCamera</a></li><li data-name="KmlDataSource"><a href="KmlDataSource.html">KmlDataSource</a></li><li data-name="KmlFeatureData"><a href="KmlFeatureData.html">KmlFeatureData</a></li><li data-name="KmlLookAt"><a href="KmlLookAt.html">KmlLookAt</a></li><li data-name="KmlTour"><a href="KmlTour.html">KmlTour</a></li><li data-name="KmlTourFlyTo"><a href="KmlTourFlyTo.html">KmlTourFlyTo</a></li><li data-name="KmlTourWait"><a href="KmlTourWait.html">KmlTourWait</a></li><li data-name="Label"><a href="Label.html">Label</a></li><li data-name="LabelCollection"><a href="LabelCollection.html">LabelCollection</a></li><li data-name="LabelGraphics"><a href="LabelGraphics.html">LabelGraphics</a></li><li data-name="LabelStyle"><a href="global.html#LabelStyle">LabelStyle</a></li><li data-name="LabelVisualizer"><a href="LabelVisualizer.html">LabelVisualizer</a></li><li data-name="LagrangePolynomialApproximation"><a href="LagrangePolynomialApproximation.html">LagrangePolynomialApproximation</a></li><li data-name="LeapSecond"><a href="LeapSecond.html">LeapSecond</a></li><li data-name="Light"><a href="Light.html">Light</a></li><li data-name="LightingModel"><a href="global.html#LightingModel">LightingModel</a></li><li data-name="LinearApproximation"><a href="LinearApproximation.html">LinearApproximation</a></li><li data-name="LinearSpline"><a href="LinearSpline.html">LinearSpline</a></li><li data-name="loadGltfJson"><a href="global.html#loadGltfJson">loadGltfJson</a></li><li data-name="LRUCache"><a href="LRUCache.html">LRUCache</a></li><li data-name="MapboxImageryProvider"><a href="MapboxImageryProvider.html">MapboxImageryProvider</a></li><li data-name="MapboxStyleImageryProvider"><a href="MapboxStyleImageryProvider.html">MapboxStyleImageryProvider</a></li><li data-name="MapMode2D"><a href="global.html#MapMode2D">MapMode2D</a></li><li data-name="MapProjection"><a href="MapProjection.html">MapProjection</a></li><li data-name="Material"><a href="Material.html">Material</a></li><li data-name="MaterialAppearance"><a href="MaterialAppearance.html">MaterialAppearance</a></li><li data-name="MaterialSupport"><a href="MaterialAppearance.MaterialSupport.html">MaterialSupport</a></li><li data-name="MaterialProperty"><a href="MaterialProperty.html">MaterialProperty</a></li><li data-name="Math"><a href="Math.html">Math</a></li><li data-name="Matrix2"><a href="Matrix2.html">Matrix2</a></li><li data-name="Matrix3"><a href="Matrix3.html">Matrix3</a></li><li data-name="Matrix4"><a href="Matrix4.html">Matrix4</a></li><li data-name="mergeSort"><a href="global.html#mergeSort">mergeSort</a></li><li data-name="mergeSortComparator"><a href="global.html#mergeSortComparator">mergeSortComparator</a></li><li data-name="metadata"><a href="global.html#metadata">metadata</a></li><li data-name="MetadataClass"><a href="MetadataClass.html">MetadataClass</a></li><li data-name="MetadataClassProperty"><a href="MetadataClassProperty.html">MetadataClassProperty</a></li><li data-name="MetadataComponentType"><a href="global.html#MetadataComponentType">MetadataComponentType</a></li><li data-name="MetadataEnum"><a href="MetadataEnum.html">MetadataEnum</a></li><li data-name="MetadataEnumValue"><a href="MetadataEnumValue.html">MetadataEnumValue</a></li><li data-name="metadataProperty"><a href="global.html#metadataProperty">metadataProperty</a></li><li data-name="MetadataSchema"><a href="MetadataSchema.html">MetadataSchema</a></li><li data-name="MetadataType"><a href="global.html#MetadataType">MetadataType</a></li><li data-name="MetadataValue"><a href="global.html#MetadataValue">MetadataValue</a></li><li data-name="Model"><a href="Model.html">Model</a></li><li data-name="ModelAnimation"><a href="ModelAnimation.html">ModelAnimation</a></li><li data-name="ModelAnimationCollection"><a href="ModelAnimationCollection.html">ModelAnimationCollection</a></li><li data-name="ModelAnimationLoop"><a href="global.html#ModelAnimationLoop">ModelAnimationLoop</a></li><li data-name="ModelFeature"><a href="ModelFeature.html">ModelFeature</a></li><li data-name="ModelGraphics"><a href="ModelGraphics.html">ModelGraphics</a></li><li data-name="ModelNode"><a href="ModelNode.html">ModelNode</a></li><li data-name="ModelVisualizer"><a href="ModelVisualizer.html">ModelVisualizer</a></li><li data-name="Moon"><a href="Moon.html">Moon</a></li><li data-name="MorphWeightSpline"><a href="MorphWeightSpline.html">MorphWeightSpline</a></li><li data-name="NearFarScalar"><a href="NearFarScalar.html">NearFarScalar</a></li><li data-name="NeverTileDiscardPolicy"><a href="NeverTileDiscardPolicy.html">NeverTileDiscardPolicy</a></li><li data-name="NodeTransformationProperty"><a href="NodeTransformationProperty.html">NodeTransformationProperty</a></li><li data-name="objectToQuery"><a href="global.html#objectToQuery">objectToQuery</a></li><li data-name="obtainTranslucentCommandExecutionFunction"><a href="global.html#obtainTranslucentCommandExecutionFunction">obtainTranslucentCommandExecutionFunction</a></li><li data-name="Occluder"><a href="Occluder.html">Occluder</a></li><li data-name="of"><a href="global.html#of">of</a></li><li data-name="OpenCageGeocoderService"><a href="OpenCageGeocoderService.html">OpenCageGeocoderService</a></li><li data-name="OpenStreetMapImageryProvider"><a href="OpenStreetMapImageryProvider.html">OpenStreetMapImageryProvider</a></li><li data-name="OrientedBoundingBox"><a href="OrientedBoundingBox.html">OrientedBoundingBox</a></li><li data-name="OrthographicFrustum"><a href="OrthographicFrustum.html">OrthographicFrustum</a></li><li data-name="OrthographicOffCenterFrustum"><a href="OrthographicOffCenterFrustum.html">OrthographicOffCenterFrustum</a></li><li data-name="PackableForInterpolation"><a href="PackableForInterpolation.html">PackableForInterpolation</a></li><li data-name="Particle"><a href="Particle.html">Particle</a></li><li data-name="ParticleBurst"><a href="ParticleBurst.html">ParticleBurst</a></li><li data-name="ParticleEmitter"><a href="ParticleEmitter.html">ParticleEmitter</a></li><li data-name="ParticleSystem"><a href="ParticleSystem.html">ParticleSystem</a></li><li data-name="PathGraphics"><a href="PathGraphics.html">PathGraphics</a></li><li data-name="PathVisualizer"><a href="PathVisualizer.html">PathVisualizer</a></li><li data-name="PeliasGeocoderService"><a href="PeliasGeocoderService.html">PeliasGeocoderService</a></li><li data-name="PENDING"><a href="global.html#PENDING">PENDING</a></li><li data-name="PerInstanceColorAppearance"><a href="PerInstanceColorAppearance.html">PerInstanceColorAppearance</a></li><li data-name="PerspectiveFrustum"><a href="PerspectiveFrustum.html">PerspectiveFrustum</a></li><li data-name="PerspectiveOffCenterFrustum"><a href="PerspectiveOffCenterFrustum.html">PerspectiveOffCenterFrustum</a></li><li data-name="PickedMetadataInfo"><a href="global.html#PickedMetadataInfo">PickedMetadataInfo</a></li><li data-name="PinBuilder"><a href="PinBuilder.html">PinBuilder</a></li><li data-name="PixelDatatype"><a href="global.html#PixelDatatype">PixelDatatype</a></li><li data-name="PixelFormat"><a href="global.html#PixelFormat">PixelFormat</a></li><li data-name="Plane"><a href="Plane.html">Plane</a></li><li data-name="PlaneGeometry"><a href="PlaneGeometry.html">PlaneGeometry</a></li><li data-name="PlaneGeometryUpdater"><a href="PlaneGeometryUpdater.html">PlaneGeometryUpdater</a></li><li data-name="PlaneGraphics"><a href="PlaneGraphics.html">PlaneGraphics</a></li><li data-name="PlaneOutlineGeometry"><a href="PlaneOutlineGeometry.html">PlaneOutlineGeometry</a></li><li data-name="PointCloudShading"><a href="PointCloudShading.html">PointCloudShading</a></li><li data-name="PointGraphics"><a href="PointGraphics.html">PointGraphics</a></li><li data-name="pointInsideTriangle"><a href="global.html#pointInsideTriangle">pointInsideTriangle</a></li><li data-name="PointPrimitive"><a href="PointPrimitive.html">PointPrimitive</a></li><li data-name="PointPrimitiveCollection"><a href="PointPrimitiveCollection.html">PointPrimitiveCollection</a></li><li data-name="PointVisualizer"><a href="PointVisualizer.html">PointVisualizer</a></li><li data-name="PolygonGeometry"><a href="PolygonGeometry.html">PolygonGeometry</a></li><li data-name="PolygonGeometryUpdater"><a href="PolygonGeometryUpdater.html">PolygonGeometryUpdater</a></li><li data-name="PolygonGraphics"><a href="PolygonGraphics.html">PolygonGraphics</a></li><li data-name="PolygonHierarchy"><a href="PolygonHierarchy.html">PolygonHierarchy</a></li><li data-name="PolygonOutlineGeometry"><a href="PolygonOutlineGeometry.html">PolygonOutlineGeometry</a></li><li data-name="Polyline"><a href="Polyline.html">Polyline</a></li><li data-name="PolylineArrowMaterialProperty"><a href="PolylineArrowMaterialProperty.html">PolylineArrowMaterialProperty</a></li><li data-name="PolylineCollection"><a href="PolylineCollection.html">PolylineCollection</a></li><li data-name="PolylineColorAppearance"><a href="PolylineColorAppearance.html">PolylineColorAppearance</a></li><li data-name="PolylineDashMaterialProperty"><a href="PolylineDashMaterialProperty.html">PolylineDashMaterialProperty</a></li><li data-name="PolylineGeometry"><a href="PolylineGeometry.html">PolylineGeometry</a></li><li data-name="PolylineGeometryUpdater"><a href="PolylineGeometryUpdater.html">PolylineGeometryUpdater</a></li><li data-name="PolylineGlowMaterialProperty"><a href="PolylineGlowMaterialProperty.html">PolylineGlowMaterialProperty</a></li><li data-name="PolylineGraphics"><a href="PolylineGraphics.html">PolylineGraphics</a></li><li data-name="PolylineMaterialAppearance"><a href="PolylineMaterialAppearance.html">PolylineMaterialAppearance</a></li><li data-name="PolylineOutlineMaterialProperty"><a href="PolylineOutlineMaterialProperty.html">PolylineOutlineMaterialProperty</a></li><li data-name="PolylineVisualizer"><a href="PolylineVisualizer.html">PolylineVisualizer</a></li><li data-name="PolylineVolumeGeometry"><a href="PolylineVolumeGeometry.html">PolylineVolumeGeometry</a></li><li data-name="PolylineVolumeGeometryUpdater"><a href="PolylineVolumeGeometryUpdater.html">PolylineVolumeGeometryUpdater</a></li><li data-name="PolylineVolumeGraphics"><a href="PolylineVolumeGraphics.html">PolylineVolumeGraphics</a></li><li data-name="PolylineVolumeOutlineGeometry"><a href="PolylineVolumeOutlineGeometry.html">PolylineVolumeOutlineGeometry</a></li><li data-name="PositionProperty"><a href="PositionProperty.html">PositionProperty</a></li><li data-name="PositionPropertyArray"><a href="PositionPropertyArray.html">PositionPropertyArray</a></li><li data-name="PostProcessStage"><a href="PostProcessStage.html">PostProcessStage</a></li><li data-name="PostProcessStageCollection"><a href="PostProcessStageCollection.html">PostProcessStageCollection</a></li><li data-name="PostProcessStageComposite"><a href="PostProcessStageComposite.html">PostProcessStageComposite</a></li><li data-name="PostProcessStageLibrary"><a href="PostProcessStageLibrary.html">PostProcessStageLibrary</a></li><li data-name="PostProcessStageSampleMode"><a href="global.html#PostProcessStageSampleMode">PostProcessStageSampleMode</a></li><li data-name="Primitive"><a href="Primitive.html">Primitive</a></li><li data-name="PrimitiveCollection"><a href="PrimitiveCollection.html">PrimitiveCollection</a></li><li data-name="PrimitiveType"><a href="global.html#PrimitiveType">PrimitiveType</a></li><li data-name="Property"><a href="Property.html">Property</a></li><li data-name="PropertyArray"><a href="PropertyArray.html">PropertyArray</a></li><li data-name="PropertyBag"><a href="PropertyBag.html">PropertyBag</a></li><li data-name="propertyName"><a href="global.html#propertyName">propertyName</a></li><li data-name="Proxy"><a href="Proxy.html">Proxy</a></li><li data-name="QuadraticRealPolynomial"><a href="QuadraticRealPolynomial.html">QuadraticRealPolynomial</a></li><li data-name="QuantizedMeshTerrainData"><a href="QuantizedMeshTerrainData.html">QuantizedMeshTerrainData</a></li><li data-name="QuarticRealPolynomial"><a href="QuarticRealPolynomial.html">QuarticRealPolynomial</a></li><li data-name="Quaternion"><a href="Quaternion.html">Quaternion</a></li><li data-name="QuaternionSpline"><a href="QuaternionSpline.html">QuaternionSpline</a></li><li data-name="queryToObject"><a href="global.html#queryToObject">queryToObject</a></li><li data-name="Queue"><a href="Queue.html">Queue</a></li><li data-name="Ray"><a href="Ray.html">Ray</a></li><li data-name="Rectangle"><a href="Rectangle.html">Rectangle</a></li><li data-name="RectangleGeometry"><a href="RectangleGeometry.html">RectangleGeometry</a></li><li data-name="RectangleGeometryUpdater"><a href="RectangleGeometryUpdater.html">RectangleGeometryUpdater</a></li><li data-name="RectangleGraphics"><a href="RectangleGraphics.html">RectangleGraphics</a></li><li data-name="RectangleOutlineGeometry"><a href="RectangleOutlineGeometry.html">RectangleOutlineGeometry</a></li><li data-name="ReferenceFrame"><a href="global.html#ReferenceFrame">ReferenceFrame</a></li><li data-name="ReferenceProperty"><a href="ReferenceProperty.html">ReferenceProperty</a></li><li data-name="removeExtension"><a href="global.html#removeExtension">removeExtension</a></li><li data-name="Request"><a href="Request.html">Request</a></li><li data-name="RequestErrorEvent"><a href="RequestErrorEvent.html">RequestErrorEvent</a></li><li data-name="RequestScheduler"><a href="RequestScheduler.html">RequestScheduler</a></li><li data-name="RequestState"><a href="global.html#RequestState">RequestState</a></li><li data-name="RequestType"><a href="global.html#RequestType">RequestType</a></li><li data-name="Resource"><a href="Resource.html">Resource</a></li><li data-name="RuntimeError"><a href="RuntimeError.html">RuntimeError</a></li><li data-name="SampledPositionProperty"><a href="SampledPositionProperty.html">SampledPositionProperty</a></li><li data-name="SampledProperty"><a href="SampledProperty.html">SampledProperty</a></li><li data-name="sampleTerrain"><a href="global.html#sampleTerrain">sampleTerrain</a></li><li data-name="sampleTerrainMostDetailed"><a href="global.html#sampleTerrainMostDetailed">sampleTerrainMostDetailed</a></li><li data-name="Scene"><a href="Scene.html">Scene</a></li><li data-name="SceneMode"><a href="global.html#SceneMode">SceneMode</a></li><li data-name="SceneTransforms"><a href="SceneTransforms.html">SceneTransforms</a></li><li data-name="schemaId"><a href="global.html#schemaId">schemaId</a></li><li data-name="ScreenSpaceCameraController"><a href="ScreenSpaceCameraController.html">ScreenSpaceCameraController</a></li><li data-name="ScreenSpaceEventHandler"><a href="ScreenSpaceEventHandler.html">ScreenSpaceEventHandler</a></li><li data-name="ScreenSpaceEventType"><a href="global.html#ScreenSpaceEventType">ScreenSpaceEventType</a></li><li data-name="SensorVolumePortionToDisplay"><a href="global.html#SensorVolumePortionToDisplay">SensorVolumePortionToDisplay</a></li><li data-name="ShadowMap"><a href="ShadowMap.html">ShadowMap</a></li><li data-name="ShadowMode"><a href="global.html#ShadowMode">ShadowMode</a></li><li data-name="ShowGeometryInstanceAttribute"><a href="ShowGeometryInstanceAttribute.html">ShowGeometryInstanceAttribute</a></li><li data-name="Simon1994PlanetaryPositions"><a href="Simon1994PlanetaryPositions.html">Simon1994PlanetaryPositions</a></li><li data-name="SimplePolylineGeometry"><a href="SimplePolylineGeometry.html">SimplePolylineGeometry</a></li><li data-name="SingleTileImageryProvider"><a href="SingleTileImageryProvider.html">SingleTileImageryProvider</a></li><li data-name="SkyAtmosphere"><a href="SkyAtmosphere.html">SkyAtmosphere</a></li><li data-name="SkyBox"><a href="SkyBox.html">SkyBox</a></li><li data-name="Spdcf"><a href="Spdcf.html">Spdcf</a></li><li data-name="SphereEmitter"><a href="SphereEmitter.html">SphereEmitter</a></li><li data-name="SphereGeometry"><a href="SphereGeometry.html">SphereGeometry</a></li><li data-name="SphereOutlineGeometry"><a href="SphereOutlineGeometry.html">SphereOutlineGeometry</a></li><li data-name="Spherical"><a href="Spherical.html">Spherical</a></li><li data-name="Spline"><a href="Spline.html">Spline</a></li><li data-name="SplitDirection"><a href="global.html#SplitDirection">SplitDirection</a></li><li data-name="srgbToLinear"><a href="global.html#srgbToLinear">srgbToLinear</a></li><li data-name="StencilFunction"><a href="global.html#StencilFunction">StencilFunction</a></li><li data-name="StencilOperation"><a href="global.html#StencilOperation">StencilOperation</a></li><li data-name="SteppedSpline"><a href="SteppedSpline.html">SteppedSpline</a></li><li data-name="Stereographic"><a href="global.html#Stereographic">Stereographic</a></li><li data-name="StorageType"><a href="global.html#StorageType">StorageType</a></li><li data-name="StripeMaterialProperty"><a href="StripeMaterialProperty.html">StripeMaterialProperty</a></li><li data-name="StripeOrientation"><a href="global.html#StripeOrientation">StripeOrientation</a></li><li data-name="StyleExpression"><a href="StyleExpression.html">StyleExpression</a></li><li data-name="subdivideArray"><a href="global.html#subdivideArray">subdivideArray</a></li><li data-name="Sun"><a href="Sun.html">Sun</a></li><li data-name="SunLight"><a href="SunLight.html">SunLight</a></li><li data-name="TaskProcessor"><a href="TaskProcessor.html">TaskProcessor</a></li><li data-name="Terrain"><a href="Terrain.html">Terrain</a></li><li data-name="TerrainData"><a href="TerrainData.html">TerrainData</a></li><li data-name="TerrainProvider"><a href="TerrainProvider.html">TerrainProvider</a></li><li data-name="TextureMagnificationFilter"><a href="global.html#TextureMagnificationFilter">TextureMagnificationFilter</a></li><li data-name="TextureMinificationFilter"><a href="global.html#TextureMinificationFilter">TextureMinificationFilter</a></li><li data-name="TextureUniform"><a href="TextureUniform.html">TextureUniform</a></li><li data-name="TILE_SIZE"><a href="global.html#TILE_SIZE">TILE_SIZE</a></li><li data-name="TileAvailability"><a href="TileAvailability.html">TileAvailability</a></li><li data-name="TileCoordinatesImageryProvider"><a href="TileCoordinatesImageryProvider.html">TileCoordinatesImageryProvider</a></li><li data-name="TileDiscardPolicy"><a href="TileDiscardPolicy.html">TileDiscardPolicy</a></li><li data-name="TileMapServiceImageryProvider"><a href="TileMapServiceImageryProvider.html">TileMapServiceImageryProvider</a></li><li data-name="TileProviderError"><a href="TileProviderError.html">TileProviderError</a></li><li data-name="TilingScheme"><a href="TilingScheme.html">TilingScheme</a></li><li data-name="TimeDynamicImagery"><a href="TimeDynamicImagery.html">TimeDynamicImagery</a></li><li data-name="TimeDynamicPointCloud"><a href="TimeDynamicPointCloud.html">TimeDynamicPointCloud</a></li><li data-name="TimeInterval"><a href="TimeInterval.html">TimeInterval</a></li><li data-name="TimeIntervalCollection"><a href="TimeIntervalCollection.html">TimeIntervalCollection</a></li><li data-name="TimeIntervalCollectionPositionProperty"><a href="TimeIntervalCollectionPositionProperty.html">TimeIntervalCollectionPositionProperty</a></li><li data-name="TimeIntervalCollectionProperty"><a href="TimeIntervalCollectionProperty.html">TimeIntervalCollectionProperty</a></li><li data-name="TimeStandard"><a href="global.html#TimeStandard">TimeStandard</a></li><li data-name="Tonemapper"><a href="global.html#Tonemapper">Tonemapper</a></li><li data-name="TrackingReferenceFrame"><a href="global.html#TrackingReferenceFrame">TrackingReferenceFrame</a></li><li data-name="Transforms"><a href="Transforms.html">Transforms</a></li><li data-name="TranslationRotationScale"><a href="TranslationRotationScale.html">TranslationRotationScale</a></li><li data-name="TridiagonalSystemSolver"><a href="TridiagonalSystemSolver.html">TridiagonalSystemSolver</a></li><li data-name="TrustedServers"><a href="TrustedServers.html">TrustedServers</a></li><li data-name="unapplyValueTransform"><a href="global.html#unapplyValueTransform">unapplyValueTransform</a></li><li data-name="UniformSpecifier"><a href="global.html#UniformSpecifier">UniformSpecifier</a></li><li data-name="UniformType"><a href="global.html#UniformType">UniformType</a></li><li data-name="unnormalize"><a href="global.html#unnormalize">unnormalize</a></li><li data-name="UrlTemplateImageryProvider"><a href="UrlTemplateImageryProvider.html">UrlTemplateImageryProvider</a></li><li data-name="VaryingType"><a href="global.html#VaryingType">VaryingType</a></li><li data-name="VelocityOrientationProperty"><a href="VelocityOrientationProperty.html">VelocityOrientationProperty</a></li><li data-name="VelocityVectorProperty"><a href="VelocityVectorProperty.html">VelocityVectorProperty</a></li><li data-name="VertexFormat"><a href="VertexFormat.html">VertexFormat</a></li><li data-name="VerticalOrigin"><a href="global.html#VerticalOrigin">VerticalOrigin</a></li><li data-name="VideoSynchronizer"><a href="VideoSynchronizer.html">VideoSynchronizer</a></li><li data-name="ViewportQuad"><a href="ViewportQuad.html">ViewportQuad</a></li><li data-name="Visibility"><a href="global.html#Visibility">Visibility</a></li><li data-name="Visualizer"><a href="Visualizer.html">Visualizer</a></li><li data-name="VoxelCell"><a href="VoxelCell.html">VoxelCell</a></li><li data-name="VoxelContent"><a href="VoxelContent.html">VoxelContent</a></li><li data-name="VoxelPrimitive"><a href="VoxelPrimitive.html">VoxelPrimitive</a></li><li data-name="VoxelProvider"><a href="VoxelProvider.html">VoxelProvider</a></li><li data-name="VoxelShapeType"><a href="global.html#VoxelShapeType">VoxelShapeType</a></li><li data-name="VRTheWorldTerrainProvider"><a href="VRTheWorldTerrainProvider.html">VRTheWorldTerrainProvider</a></li><li data-name="WallGeometry"><a href="WallGeometry.html">WallGeometry</a></li><li data-name="WallGeometryUpdater"><a href="WallGeometryUpdater.html">WallGeometryUpdater</a></li><li data-name="WallGraphics"><a href="WallGraphics.html">WallGraphics</a></li><li data-name="WallOutlineGeometry"><a href="WallOutlineGeometry.html">WallOutlineGeometry</a></li><li data-name="WebGLConstants"><a href="global.html#WebGLConstants">WebGLConstants</a></li><li data-name="WebGLOptions"><a href="global.html#WebGLOptions">WebGLOptions</a></li><li data-name="WebMapServiceImageryProvider"><a href="WebMapServiceImageryProvider.html">WebMapServiceImageryProvider</a></li><li data-name="WebMapTileServiceImageryProvider"><a href="WebMapTileServiceImageryProvider.html">WebMapTileServiceImageryProvider</a></li><li data-name="WebMercatorProjection"><a href="WebMercatorProjection.html">WebMercatorProjection</a></li><li data-name="WebMercatorTilingScheme"><a href="WebMercatorTilingScheme.html">WebMercatorTilingScheme</a></li><li data-name="WindingOrder"><a href="global.html#WindingOrder">WindingOrder</a></li><li data-name="writeTextToCanvas"><a href="global.html#writeTextToCanvas">writeTextToCanvas</a></li></ul><h5>packages/widgets</h5><ul><li data-name="Animation"><a href="Animation.html">Animation</a></li><li data-name="AnimationViewModel"><a href="AnimationViewModel.html">AnimationViewModel</a></li><li data-name="BaseLayerPicker"><a href="BaseLayerPicker.html">BaseLayerPicker</a></li><li data-name="BaseLayerPickerViewModel"><a href="BaseLayerPickerViewModel.html">BaseLayerPickerViewModel</a></li><li data-name="Cesium3DTilesInspector"><a href="Cesium3DTilesInspector.html">Cesium3DTilesInspector</a></li><li data-name="Cesium3DTilesInspectorViewModel"><a href="Cesium3DTilesInspectorViewModel.html">Cesium3DTilesInspectorViewModel</a></li><li data-name="CesiumInspector"><a href="CesiumInspector.html">CesiumInspector</a></li><li data-name="CesiumInspectorViewModel"><a href="CesiumInspectorViewModel.html">CesiumInspectorViewModel</a></li><li data-name="ClockViewModel"><a href="ClockViewModel.html">ClockViewModel</a></li><li data-name="Command"><a href="Command.html">Command</a></li><li data-name="createCommand"><a href="global.html#createCommand">createCommand</a></li><li data-name="FullscreenButton"><a href="FullscreenButton.html">FullscreenButton</a></li><li data-name="FullscreenButtonViewModel"><a href="FullscreenButtonViewModel.html">FullscreenButtonViewModel</a></li><li data-name="Geocoder"><a href="Geocoder.html">Geocoder</a></li><li data-name="GeocoderViewModel"><a href="GeocoderViewModel.html">GeocoderViewModel</a></li><li data-name="HomeButton"><a href="HomeButton.html">HomeButton</a></li><li data-name="HomeButtonViewModel"><a href="HomeButtonViewModel.html">HomeButtonViewModel</a></li><li data-name="I3sBslExplorerViewModel"><a href="I3sBslExplorerViewModel.html">I3sBslExplorerViewModel</a></li><li data-name="I3SBuildingSceneLayerExplorer"><a href="I3SBuildingSceneLayerExplorer.html">I3SBuildingSceneLayerExplorer</a></li><li data-name="InfoBox"><a href="InfoBox.html">InfoBox</a></li><li data-name="InfoBoxViewModel"><a href="InfoBoxViewModel.html">InfoBoxViewModel</a></li><li data-name="NavigationHelpButton"><a href="NavigationHelpButton.html">NavigationHelpButton</a></li><li data-name="NavigationHelpButtonViewModel"><a href="NavigationHelpButtonViewModel.html">NavigationHelpButtonViewModel</a></li><li data-name="PerformanceWatchdog"><a href="PerformanceWatchdog.html">PerformanceWatchdog</a></li><li data-name="PerformanceWatchdogViewModel"><a href="PerformanceWatchdogViewModel.html">PerformanceWatchdogViewModel</a></li><li data-name="ProjectionPicker"><a href="ProjectionPicker.html">ProjectionPicker</a></li><li data-name="ProjectionPickerViewModel"><a href="ProjectionPickerViewModel.html">ProjectionPickerViewModel</a></li><li data-name="ProviderViewModel"><a href="ProviderViewModel.html">ProviderViewModel</a></li><li data-name="SceneModePicker"><a href="SceneModePicker.html">SceneModePicker</a></li><li data-name="SceneModePickerViewModel"><a href="SceneModePickerViewModel.html">SceneModePickerViewModel</a></li><li data-name="SelectionIndicator"><a href="SelectionIndicator.html">SelectionIndicator</a></li><li data-name="SelectionIndicatorViewModel"><a href="SelectionIndicatorViewModel.html">SelectionIndicatorViewModel</a></li><li data-name="SvgPathBindingHandler"><a href="SvgPathBindingHandler.html">SvgPathBindingHandler</a></li><li data-name="Timeline"><a href="Timeline.html">Timeline</a></li><li data-name="ToggleButtonViewModel"><a href="ToggleButtonViewModel.html">ToggleButtonViewModel</a></li><li data-name="Viewer"><a href="Viewer.html">Viewer</a></li><li data-name="viewerCesium3DTilesInspectorMixin"><a href="global.html#viewerCesium3DTilesInspectorMixin">viewerCesium3DTilesInspectorMixin</a></li><li data-name="viewerCesiumInspectorMixin"><a href="global.html#viewerCesiumInspectorMixin">viewerCesiumInspectorMixin</a></li><li data-name="viewerDragDropMixin"><a href="global.html#viewerDragDropMixin">viewerDragDropMixin</a></li><li data-name="viewerPerformanceWatchdogMixin"><a href="global.html#viewerPerformanceWatchdogMixin">viewerPerformanceWatchdogMixin</a></li><li data-name="viewerVoxelInspectorMixin"><a href="global.html#viewerVoxelInspectorMixin">viewerVoxelInspectorMixin</a></li><li data-name="VoxelInspector"><a href="VoxelInspector.html">VoxelInspector</a></li><li data-name="VoxelInspectorViewModel"><a href="VoxelInspectorViewModel.html">VoxelInspectorViewModel</a></li><li data-name="VRButton"><a href="VRButton.html">VRButton</a></li><li data-name="VRButtonViewModel"><a href="VRButtonViewModel.html">VRButtonViewModel</a></li></ul></div>
    </div>
</div>

<script>
if (window.frameElement) {
    document.body.className = 'embedded';

    var ele = document.createElement('a');
    ele.className = 'popout';
    ele.target = '_blank';
    ele.href = window.location.href;
    ele.title = 'Pop out';
    document.getElementById('main').appendChild(ele);
}

// Set targets on external links.  Sandcastle and GitHub shouldn't be embedded in any iframe.
Array.prototype.forEach.call(document.getElementsByTagName('a'), function(a) {
    if (/^https?:/i.test(a.getAttribute('href'))) {
        a.target='_blank';
    }
});
</script>

<script src="javascript/prism.js"></script>
<script src="javascript/cesiumDoc.js"></script>

</body>
</html>