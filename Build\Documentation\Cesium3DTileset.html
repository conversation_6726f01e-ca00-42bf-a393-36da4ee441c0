<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Cesium3DTileset - Cesium Documentation</title>

    <!--[if lt IE 9]>
      <script src="javascript/html5.js"></script>
    <![endif]-->
    <link href="styles/jsdoc-default.css" rel="stylesheet">
    <link href="styles/prism.css" rel="stylesheet">
</head>
<body>

<div id="main">

    <h1 class="page-title">
        <a href="index.html"><img src="Images/CesiumLogo.png" class="cesiumLogo"></a>
        Cesium3DTileset
        <div class="titleCenterer"></div>
    </h1>

    




<section>

<header>
    
</header>

<article>
    <div class="container-overview">
    

    
        
    <div class="nameContainer">
    <h4 class="name" id="Cesium3DTileset">
        <a href="#Cesium3DTileset" class="doc-link"></a>
        new Cesium.Cesium3DTileset<span class="signature">(options)</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L203">engine/Source/Scene/Cesium3DTileset.js 203</a>
</div>


    </h4>

    </div>

    


<div class="description">
    A <a href="https://github.com/CesiumGS/3d-tiles/tree/main/specification">3D Tiles tileset</a>,
used for streaming massive heterogeneous 3D geospatial datasets.

<div class="notice">
This object is normally not instantiated directly, use <a href="Cesium3DTileset.html#.fromUrl"><code>Cesium3DTileset.fromUrl</code></a>.
</div>
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cesium3DTileset.html#.ConstructorOptions">Cesium3DTileset.ConstructorOptions</a></span>


            
            </td>

            

            <td class="description last">
            
                An object describing initialization options</td>
        </tr>

    
    </tbody>
</table>













<h5>Throws:</h5>
<ul>
        <li>

    <div class="param-desc">
        
<span class="param-type"><a href="DeveloperError.html">DeveloperError</a></span>

: The tileset must be 3D Tiles version 0.0 or 1.0.
    </div>

</li>
    </ul>



<dl class="details">


    

    

    

    

    

    

    

    

    
        <h5>Examples:</h5>
        
    <pre><code class="language-javascript">try {
  const tileset = await Cesium.Cesium3DTileset.fromUrl(
     "http://localhost:8002/tilesets/Seattle/tileset.json"
  );
  scene.primitives.add(tileset);
} catch (error) {
  console.error(`Error creating tileset: ${error}`);
}</code></pre>

    <pre><code class="language-javascript">// Turn on camera collisions with the tileset.
try {
  const tileset = await Cesium.Cesium3DTileset.fromUrl(
     "http://localhost:8002/tilesets/Seattle/tileset.json",
     { enableCollision: true }
  );
  scene.primitives.add(tileset);
} catch (error) {
  console.error(`Error creating tileset: ${error}`);
}</code></pre>

    <pre><code class="language-javascript">// Common setting for the skipLevelOfDetail optimization
const tileset = await Cesium.Cesium3DTileset.fromUrl(
  "http://localhost:8002/tilesets/Seattle/tileset.json", {
     skipLevelOfDetail: true,
     baseScreenSpaceError: 1024,
     skipScreenSpaceErrorFactor: 16,
     skipLevels: 1,
     immediatelyLoadDesiredLevelOfDetail: false,
     loadSiblings: false,
     cullWithChildrenBounds: true
});
scene.primitives.add(tileset);</code></pre>

    <pre><code class="language-javascript">// Common settings for the dynamicScreenSpaceError optimization
const tileset = await Cesium.Cesium3DTileset.fromUrl(
  "http://localhost:8002/tilesets/Seattle/tileset.json", {
     dynamicScreenSpaceError: true,
     dynamicScreenSpaceErrorDensity: 2.0e-4,
     dynamicScreenSpaceErrorFactor: 24.0,
     dynamicScreenSpaceErrorHeightFalloff: 0.25
});
scene.primitives.add(tileset);</code></pre>

    

    

    

    
    <div class="tag-experimental">
        <h5>Experimental</h5>
        
            <p>Support for loading Gaussian splats content encoded with SPZ compression using the draft glTF extension <a href="https://github.com/KhronosGroup/glTF/pull/2490%20"> KHR_spz_gaussian_splats_compression</a> is experimental and is subject change without Cesium's standard deprecation policy.</p>
        
    </div>
    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="https://github.com/CesiumGS/3d-tiles/tree/main/specification">3D Tiles specification</a></li>
    </ul>
    

    
</dl>


    
    </div>

    

    

    

    

    

    
        <h3 class="subsection-title">Members</h3>

        
            
<div class="nameContainer">
<h4 class="name" id="allTilesLoaded">
    <a href="#allTilesLoaded" class="doc-link"></a>
    allTilesLoaded<span class="type-signature"> : <a href="Event.html">Event</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L617">engine/Source/Scene/Cesium3DTileset.js 617</a>
</div>


</h4>

</div>



<div class="description">
    The event fired to indicate that all tiles that meet the screen space error this frame are loaded. The tileset
is completely loaded for this view.
<p>
This event is fired at the end of the frame after the scene is rendered.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">new Event()</code>
    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">tileset.allTilesLoaded.addEventListener(function() {
    console.log('All tiles are loaded');
});</code></pre>

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Cesium3DTileset.html#tilesLoaded">Cesium3DTileset#tilesLoaded</a></li>
    </ul>
    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="asset">
    <a href="#asset" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> asset<span class="type-signature"> : object</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1107">engine/Source/Scene/Cesium3DTileset.js 1107</a>
</div>


</h4>

</div>



<div class="description">
    Gets the tileset's asset object property, which contains metadata about the tileset.
<p>
See the <a href="https://github.com/CesiumGS/3d-tiles/tree/main/specification#reference-asset">asset schema reference</a>
in the 3D Tiles spec for the full set of properties.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="backFaceCulling">
    <a href="#backFaceCulling" class="doc-link"></a>
    backFaceCulling<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L873">engine/Source/Scene/Cesium3DTileset.js 873</a>
</div>


</h4>

</div>



<div class="description">
    Whether to cull back-facing geometry. When true, back face culling is determined
by the glTF material's doubleSided property; when false, back face culling is disabled.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">true</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="basePath">
    <a href="#basePath" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> <span class="type-signature attribute-deprecated">deprecated</span> basePath<span class="type-signature"> : string</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1289">engine/Source/Scene/Cesium3DTileset.js 1289</a>
</div>


</h4>

</div>



<div class="description">
    The base path that non-absolute paths in tileset JSON file are relative to.
</div>





<dl class="details">


    

    

    

    
    <p>
        <span class="details-header important">Deprecated:</span>
        <span>true</span>
    </p>
    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="baseScreenSpaceError">
    <a href="#baseScreenSpaceError" class="doc-link"></a>
    baseScreenSpaceError<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L771">engine/Source/Scene/Cesium3DTileset.js 771</a>
</div>


</h4>

</div>



<div class="description">
    The screen space error that must be reached before skipping levels of detail.
<p>
Only used when <a href="Cesium3DTileset.html#skipLevelOfDetail"><code>Cesium3DTileset#skipLevelOfDetail</code></a> is <code>true</code>.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">1024</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="boundingSphere">
    <a href="#boundingSphere" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> boundingSphere<span class="type-signature"> : <a href="BoundingSphere.html">BoundingSphere</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1661">engine/Source/Scene/Cesium3DTileset.js 1661</a>
</div>


</h4>

</div>



<div class="description">
    The tileset's bounding sphere.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">const tileset = await Cesium.Cesium3DTileset.fromUrl("http://localhost:8002/tilesets/Seattle/tileset.json");

viewer.scene.primitives.add(tileset);

// Set the camera to view the newly added tileset
viewer.camera.viewBoundingSphere(tileset.boundingSphere, new Cesium.HeadingPitchRange(0, -0.5, 0));</code></pre>

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="cacheBytes">
    <a href="#cacheBytes" class="doc-link"></a>
    cacheBytes<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1547">engine/Source/Scene/Cesium3DTileset.js 1547</a>
</div>


</h4>

</div>



<div class="description">
    The amount of GPU memory (in bytes) used to cache tiles. This memory usage is estimated from
geometry, textures, and batch table textures of loaded tiles. For point clouds, this value also
includes per-point metadata.
<p>
Tiles not in view are unloaded to enforce this.
</p>
<p>
If decreasing this value results in unloading tiles, the tiles are unloaded the next frame.
</p>
<p>
If tiles sized more than <code>cacheBytes</code> are needed to meet the
desired screen space error, determined by <a href="Cesium3DTileset.html#maximumScreenSpaceError"><code>Cesium3DTileset#maximumScreenSpaceError</code></a>,
for the current view, then the memory usage of the tiles loaded will exceed
<code>cacheBytes</code> by up to <code>maximumCacheOverflowBytes</code>.
For example, if <code>cacheBytes</code> is 500000, but 600000 bytes
of tiles are needed to meet the screen space error, then 600000 bytes of tiles
may be loaded (if <code>maximumCacheOverflowBytes</code> is at least 100000).
When these tiles go out of view, they will be unloaded.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">536870912</code>
    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Cesium3DTileset.html#totalMemoryUsageInBytes">Cesium3DTileset#totalMemoryUsageInBytes</a></li>
    </ul>
    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="classificationType">
    <a href="#classificationType" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> classificationType<span class="type-signature"> : <a href="global.html#ClassificationType">ClassificationType</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1808">engine/Source/Scene/Cesium3DTileset.js 1808</a>
</div>


</h4>

</div>



<div class="description">
    Determines whether terrain, 3D Tiles, or both will be classified by this tileset.
<p>
This option is only applied to tilesets containing batched 3D models,
glTF content, geometry data, or vector data. Even when undefined, vector
and geometry data must render as classifications and will default to
rendering on both terrain and other 3D Tiles tilesets.
</p>
<p>
When enabled for batched 3D model and glTF tilesets, there are a few
requirements/limitations on the glTF:
<ul>
    <li>The glTF cannot contain morph targets, skins, or animations.</li>
    <li>The glTF cannot contain the <code>EXT_mesh_gpu_instancing</code> extension.</li>
    <li>Only meshes with TRIANGLES can be used to classify other assets.</li>
    <li>The meshes must be watertight.</li>
    <li>The <code>POSITION</code> semantic is required.</li>
    <li>If <code>_BATCHID</code>s and an index buffer are both present, all indices with the same batch id must occupy contiguous sections of the index buffer.</li>
    <li>If <code>_BATCHID</code>s are present with no index buffer, all positions with the same batch id must occupy contiguous sections of the position buffer.</li>
</ul>
</p>
<p>
Additionally, classification is not supported for points or instanced 3D
models.
</p>
<p>
The 3D Tiles or terrain receiving the classification must be opaque.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">undefined</code>
    

    

    

    

    
    <div class="tag-experimental">
        <h5>Experimental</h5>
        
            <p>This feature is using part of the 3D Tiles spec that is not final and is subject to change without Cesium's standard deprecation policy.</p>
        
    </div>
    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="clippingPlanes">
    <a href="#clippingPlanes" class="doc-link"></a>
    clippingPlanes<span class="type-signature"> : <a href="ClippingPlaneCollection.html">ClippingPlaneCollection</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1134">engine/Source/Scene/Cesium3DTileset.js 1134</a>
</div>


</h4>

</div>



<div class="description">
    The <a href="ClippingPlaneCollection.html"><code>ClippingPlaneCollection</code></a> used to selectively disable rendering the tileset.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="clippingPolygons">
    <a href="#clippingPolygons" class="doc-link"></a>
    clippingPolygons<span class="type-signature"> : <a href="ClippingPolygonCollection.html">ClippingPolygonCollection</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1150">engine/Source/Scene/Cesium3DTileset.js 1150</a>
</div>


</h4>

</div>



<div class="description">
    The <a href="ClippingPolygonCollection.html"><code>ClippingPolygonCollection</code></a> used to selectively disable rendering the tileset.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="colorBlendAmount">
    <a href="#colorBlendAmount" class="doc-link"></a>
    colorBlendAmount<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L568">engine/Source/Scene/Cesium3DTileset.js 568</a>
</div>


</h4>

</div>



<div class="description">
    Defines the value used to linearly interpolate between the source color and feature color when the <a href="Cesium3DTileset.html#colorBlendMode"><code>Cesium3DTileset#colorBlendMode</code></a> is <code>MIX</code>.
A value of 0.0 results in the source color while a value of 1.0 results in the feature color, with any value in-between
resulting in a mix of the source color and feature color.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">0.5</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="colorBlendMode">
    <a href="#colorBlendMode" class="doc-link"></a>
    colorBlendMode<span class="type-signature"> : <a href="global.html#Cesium3DTileColorBlendMode">Cesium3DTileColorBlendMode</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L558">engine/Source/Scene/Cesium3DTileset.js 558</a>
</div>


</h4>

</div>



<div class="description">
    Defines how per-feature colors set from the Cesium API or declarative styling blend with the source colors from
the original feature, e.g. glTF material or per-point color in the tile.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">Cesium3DTileColorBlendMode.HIGHLIGHT</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="cullRequestsWhileMoving">
    <a href="#cullRequestsWhileMoving" class="doc-link"></a>
    cullRequestsWhileMoving<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L303">engine/Source/Scene/Cesium3DTileset.js 303</a>
</div>


</h4>

</div>



<div class="description">
    Optimization option. Don't request tiles that will likely be unused when they come back because of the camera's movement. This optimization only applies to stationary tilesets.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">true</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="cullRequestsWhileMovingMultiplier">
    <a href="#cullRequestsWhileMovingMultiplier" class="doc-link"></a>
    cullRequestsWhileMovingMultiplier<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L312">engine/Source/Scene/Cesium3DTileset.js 312</a>
</div>


</h4>

</div>



<div class="description">
    Optimization option. Multiplier used in culling requests while moving. Larger is more aggressive culling, smaller less aggressive culling.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">60.0</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="customShader">
    <a href="#customShader" class="doc-link"></a>
    customShader<span class="type-signature"> : <a href="CustomShader.html">CustomShader</a>|undefined</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1366">engine/Source/Scene/Cesium3DTileset.js 1366</a>
</div>


</h4>

</div>



<div class="description">
    A custom shader to apply to all tiles in the tileset. Only used for
contents that use <a href="Model.html"><code>Model</code></a>. Using custom shaders with a
<a href="Cesium3DTileStyle.html"><code>Cesium3DTileStyle</code></a> may lead to undefined behavior.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">undefined</code>
    

    

    

    

    
    <div class="tag-experimental">
        <h5>Experimental</h5>
        
            <p>This feature is using part of the 3D Tiles spec that is not final and is subject to change without Cesium's standard deprecation policy.</p>
        
    </div>
    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="debugColorizeTiles">
    <a href="#debugColorizeTiles" class="doc-link"></a>
    debugColorizeTiles<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L937">engine/Source/Scene/Cesium3DTileset.js 937</a>
</div>


</h4>

</div>



<div class="description">
    This property is for debugging only; it is not optimized for production use.
<p>
When true, assigns a random color to each tile.  This is useful for visualizing
what features belong to what tiles, especially with additive refinement where features
from parent tiles may be interleaved with features from child tiles.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="debugFreezeFrame">
    <a href="#debugFreezeFrame" class="doc-link"></a>
    debugFreezeFrame<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L924">engine/Source/Scene/Cesium3DTileset.js 924</a>
</div>


</h4>

</div>



<div class="description">
    This property is for debugging only; it is not optimized for production use.
<p>
Determines if only the tiles from last frame should be used for rendering.  This
effectively "freezes" the tileset to the previous frame so it is possible to zoom
out and see what was rendered.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="debugShowBoundingVolume">
    <a href="#debugShowBoundingVolume" class="doc-link"></a>
    debugShowBoundingVolume<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L971">engine/Source/Scene/Cesium3DTileset.js 971</a>
</div>


</h4>

</div>



<div class="description">
    This property is for debugging only; it is not optimized for production use.
<p>
When true, renders the bounding volume for each visible tile.  The bounding volume is
white if the tile has a content bounding volume or is empty; otherwise, it is red.  Tiles that don't meet the
screen space error and are still refining to their descendants are yellow.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="debugShowContentBoundingVolume">
    <a href="#debugShowContentBoundingVolume" class="doc-link"></a>
    debugShowContentBoundingVolume<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L983">engine/Source/Scene/Cesium3DTileset.js 983</a>
</div>


</h4>

</div>



<div class="description">
    This property is for debugging only; it is not optimized for production use.
<p>
When true, renders the bounding volume for each visible tile's content. The bounding volume is
blue if the tile has a content bounding volume; otherwise it is red.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="debugShowGeometricError">
    <a href="#debugShowGeometricError" class="doc-link"></a>
    debugShowGeometricError<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1016">engine/Source/Scene/Cesium3DTileset.js 1016</a>
</div>


</h4>

</div>



<div class="description">
    This property is for debugging only; it is not optimized for production use.
<p>
When true, draws labels to indicate the geometric error of each tile.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="debugShowMemoryUsage">
    <a href="#debugShowMemoryUsage" class="doc-link"></a>
    debugShowMemoryUsage<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1039">engine/Source/Scene/Cesium3DTileset.js 1039</a>
</div>


</h4>

</div>



<div class="description">
    This property is for debugging only; it is not optimized for production use.
<p>
When true, draws labels to indicate the geometry and texture memory usage of each tile.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="debugShowRenderingStatistics">
    <a href="#debugShowRenderingStatistics" class="doc-link"></a>
    debugShowRenderingStatistics<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1027">engine/Source/Scene/Cesium3DTileset.js 1027</a>
</div>


</h4>

</div>



<div class="description">
    This property is for debugging only; it is not optimized for production use.
<p>
When true, draws labels to indicate the number of commands, points, triangles and features of each tile.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="debugShowUrl">
    <a href="#debugShowUrl" class="doc-link"></a>
    debugShowUrl<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1050">engine/Source/Scene/Cesium3DTileset.js 1050</a>
</div>


</h4>

</div>



<div class="description">
    This property is for debugging only; it is not optimized for production use.
<p>
When true, draws labels to indicate the url of each tile.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="debugShowViewerRequestVolume">
    <a href="#debugShowViewerRequestVolume" class="doc-link"></a>
    debugShowViewerRequestVolume<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L995">engine/Source/Scene/Cesium3DTileset.js 995</a>
</div>


</h4>

</div>



<div class="description">
    This property is for debugging only; it is not optimized for production use.
<p>
When true, renders the viewer request volume for each tile.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="debugWireframe">
    <a href="#debugWireframe" class="doc-link"></a>
    debugWireframe<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L950">engine/Source/Scene/Cesium3DTileset.js 950</a>
</div>


</h4>

</div>



<div class="description">
    This property is for debugging only; it is not optimized for production use.
<p>
When true, renders each tile's content as a wireframe.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="dynamicScreenSpaceError">
    <a href="#dynamicScreenSpaceError" class="doc-link"></a>
    dynamicScreenSpaceError<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L436">engine/Source/Scene/Cesium3DTileset.js 436</a>
</div>


</h4>

</div>



<div class="description">
    Optimization option. For street-level horizon views, use lower resolution tiles far from the camera. This reduces
the amount of data loaded and improves tileset loading time with a slight drop in visual quality in the distance.
<p>
This optimization is strongest when the camera is close to the ground plane of the tileset and looking at the
horizon. Furthermore, the results are more accurate for tightly fitting bounding volumes like box and region.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">true</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="dynamicScreenSpaceErrorDensity">
    <a href="#dynamicScreenSpaceErrorDensity" class="doc-link"></a>
    dynamicScreenSpaceErrorDensity<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L492">engine/Source/Scene/Cesium3DTileset.js 492</a>
</div>


</h4>

</div>



<div class="description">
    Similar to <a href="Fog.html#density"><code>Fog#density</code></a>, this option controls the camera distance at which the <a href="Cesium3DTileset.html#dynamicScreenSpaceError"><code>Cesium3DTileset#dynamicScreenSpaceError</code></a>
optimization applies. Larger values will cause tiles closer to the camera to be affected. This value must be
non-negative.
<p>
This optimization works by rolling off the tile screen space error (SSE) with camera distance like a bell curve.
This has the effect of selecting lower resolution tiles far from the camera. Near the camera, no adjustment is
made. For tiles further away, the SSE is reduced by up to <a href="Cesium3DTileset.html#dynamicScreenSpaceErrorFactor"><code>Cesium3DTileset#dynamicScreenSpaceErrorFactor</code></a>
(measured in pixels of error).
</p>
<p>
Increasing the density makes the bell curve narrower so tiles closer to the camera are affected. This is analagous
to moving fog closer to the camera.
</p>
<p>
When the density is 0, the optimization will have no effect on the tileset.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">2.0e-4</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="dynamicScreenSpaceErrorFactor">
    <a href="#dynamicScreenSpaceErrorFactor" class="doc-link"></a>
    dynamicScreenSpaceErrorFactor<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L511">engine/Source/Scene/Cesium3DTileset.js 511</a>
</div>


</h4>

</div>



<div class="description">
    A parameter that controls the intensity of the <a href="Cesium3DTileset.html#dynamicScreenSpaceError"><code>Cesium3DTileset#dynamicScreenSpaceError</code></a> optimization for
tiles on the horizon. Larger values cause lower resolution tiles to load, improving runtime performance at a slight
reduction of visual quality. The value must be non-negative.
<p>
More specifically, this parameter represents the maximum adjustment to screen space error (SSE) in pixels for tiles
far away from the camera. See <a href="Cesium3DTileset.html#dynamicScreenSpaceErrorDensity"><code>Cesium3DTileset#dynamicScreenSpaceErrorDensity</code></a> for more details about how
this optimization works.
</p>
<p>
When the SSE factor is set to 0, the optimization will have no effect on the tileset.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">24.0</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="dynamicScreenSpaceErrorHeightFalloff">
    <a href="#dynamicScreenSpaceErrorHeightFalloff" class="doc-link"></a>
    dynamicScreenSpaceErrorHeightFalloff<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L523">engine/Source/Scene/Cesium3DTileset.js 523</a>
</div>


</h4>

</div>



<div class="description">
    A ratio of the tileset's height that determines "street level" for the <a href="Cesium3DTileset.html#dynamicScreenSpaceError"><code>Cesium3DTileset#dynamicScreenSpaceError</code></a>
optimization. When the camera is below this height, the dynamic screen space error optimization will have the maximum
effect, and it will roll off above this value. Valid values are between 0.0 and 1.0.
<p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">0.25</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="ellipsoid">
    <a href="#ellipsoid" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> ellipsoid<span class="type-signature"> : <a href="Ellipsoid.html">Ellipsoid</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1859">engine/Source/Scene/Cesium3DTileset.js 1859</a>
</div>


</h4>

</div>



<div class="description">
    Gets an ellipsoid describing the shape of the globe.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="enableCollision">
    <a href="#enableCollision" class="doc-link"></a>
    enableCollision<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L909">engine/Source/Scene/Cesium3DTileset.js 909</a>
</div>


</h4>

</div>



<div class="description">
    If <code>true</code>, allows collisions for camera collisions or picking. While this is  <code>true</code> the camera will be prevented from going in or below the tileset surface if <a href="ScreenSpaceCameraController.html#enableCollisionDetection"><code>ScreenSpaceCameraController#enableCollisionDetection</code></a> is true. This can have performance implecations if the tileset contains tile with a larger number of vertices.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="environmentMapManager">
    <a href="#environmentMapManager" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> environmentMapManager<span class="type-signature"> : <a href="DynamicEnvironmentMapManager.html">DynamicEnvironmentMapManager</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1978">engine/Source/Scene/Cesium3DTileset.js 1978</a>
</div>


</h4>

</div>



<div class="description">
    The properties for managing dynamic environment maps on this model. Affects lighting.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">// Change the ground color used for a tileset's environment map to a forest green
const environmentMapManager = tileset.environmentMapManager;
environmentMapManager.groundColor = Cesium.Color.fromCssColorString("#203b34");</code></pre>

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="examineVectorLinesFunction">
    <a href="#examineVectorLinesFunction" class="doc-link"></a>
    examineVectorLinesFunction<span class="type-signature"> : function</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1059">engine/Source/Scene/Cesium3DTileset.js 1059</a>
</div>


</h4>

</div>



<div class="description">
    Function for examining vector lines as they are being streamed.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    
    <div class="tag-experimental">
        <h5>Experimental</h5>
        
            <p>This feature is using part of the 3D Tiles spec that is not final and is subject to change without Cesium's standard deprecation policy.</p>
        
    </div>
    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="extensions">
    <a href="#extensions" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> extensions<span class="type-signature"> : object</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1121">engine/Source/Scene/Cesium3DTileset.js 1121</a>
</div>


</h4>

</div>



<div class="description">
    Gets the tileset's extensions object property.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="extras">
    <a href="#extras" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> extras<span class="type-signature"> : *</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1931">engine/Source/Scene/Cesium3DTileset.js 1931</a>
</div>


</h4>

</div>



<div class="description">
    Returns the <code>extras</code> property at the top-level of the tileset JSON, which contains application specific metadata.
Returns <code>undefined</code> if <code>extras</code> does not exist.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="https://github.com/CesiumGS/3d-tiles/tree/main/specification#specifying-extensions-and-application-specific-extras">Extras in the 3D Tiles specification.</a></li>
    </ul>
    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="featureIdLabel">
    <a href="#featureIdLabel" class="doc-link"></a>
    featureIdLabel<span class="type-signature"> : string</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L2057">engine/Source/Scene/Cesium3DTileset.js 2057</a>
</div>


</h4>

</div>



<div class="description">
    Label of the feature ID set to use for picking and styling.
<p>
For EXT_mesh_features, this is the feature ID's label property, or
"featureId_N" (where N is the index in the featureIds array) when not
specified. EXT_feature_metadata did not have a label field, so such
feature ID sets are always labeled "featureId_N" where N is the index in
the list of all feature Ids, where feature ID attributes are listed before
feature ID textures.
</p>
<p>
If featureIdLabel is set to an integer N, it is converted to
the string "featureId_N" automatically. If both per-primitive and
per-instance feature IDs are present, the instance feature IDs take
priority.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    
    <div class="tag-experimental">
        <h5>Experimental</h5>
        
            <p>This feature is using part of the 3D Tiles spec that is not final and is subject to change without Cesium's standard deprecation policy.</p>
        
    </div>
    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="foveatedConeSize">
    <a href="#foveatedConeSize" class="doc-link"></a>
    foveatedConeSize<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1875">engine/Source/Scene/Cesium3DTileset.js 1875</a>
</div>


</h4>

</div>



<div class="description">
    Optimization option. Used when <a href="Cesium3DTileset.html#foveatedScreenSpaceError"><code>Cesium3DTileset#foveatedScreenSpaceError</code></a> is true to control the cone size that determines which tiles are deferred.
Tiles that are inside this cone are loaded immediately. Tiles outside the cone are potentially deferred based on how far outside the cone they are and <a href="Cesium3DTileset.html#foveatedInterpolationCallback"><code>Cesium3DTileset#foveatedInterpolationCallback</code></a> and <a href="Cesium3DTileset.html#foveatedMinimumScreenSpaceErrorRelaxation"><code>Cesium3DTileset#foveatedMinimumScreenSpaceErrorRelaxation</code></a>.
Setting this to 0.0 means the cone will be the line formed by the camera position and its view direction. Setting this to 1.0 means the cone encompasses the entire field of view of the camera, essentially disabling the effect.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">0.3</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="foveatedInterpolationCallback">
    <a href="#foveatedInterpolationCallback" class="doc-link"></a>
    foveatedInterpolationCallback<span class="type-signature"> : <a href="Cesium3DTileset.html#.foveatedInterpolationCallback">Cesium3DTileset.foveatedInterpolationCallback</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L457">engine/Source/Scene/Cesium3DTileset.js 457</a>
</div>


</h4>

</div>



<div class="description">
    Gets or sets a callback to control how much to raise the screen space error for tiles outside the foveated cone,
interpolating between <a href="Cesium3DTileset.html#foveatedMinimumScreenSpaceErrorRelaxation"><code>Cesium3DTileset#foveatedMinimumScreenSpaceErrorRelaxation</code></a> and <a href="Cesium3DTileset.html#maximumScreenSpaceError"><code>Cesium3DTileset#maximumScreenSpaceError</code></a>.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="foveatedMinimumScreenSpaceErrorRelaxation">
    <a href="#foveatedMinimumScreenSpaceErrorRelaxation" class="doc-link"></a>
    foveatedMinimumScreenSpaceErrorRelaxation<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1898">engine/Source/Scene/Cesium3DTileset.js 1898</a>
</div>


</h4>

</div>



<div class="description">
    Optimization option. Used when <a href="Cesium3DTileset.html#foveatedScreenSpaceError"><code>Cesium3DTileset#foveatedScreenSpaceError</code></a> is true to control the starting screen space error relaxation for tiles outside the foveated cone.
The screen space error will be raised starting with this value up to <a href="Cesium3DTileset.html#maximumScreenSpaceError"><code>Cesium3DTileset#maximumScreenSpaceError</code></a> based on the provided <a href="Cesium3DTileset.html#foveatedInterpolationCallback"><code>Cesium3DTileset#foveatedInterpolationCallback</code></a>.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">0.0</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="foveatedScreenSpaceError">
    <a href="#foveatedScreenSpaceError" class="doc-link"></a>
    foveatedScreenSpaceError<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L446">engine/Source/Scene/Cesium3DTileset.js 446</a>
</div>


</h4>

</div>



<div class="description">
    Optimization option. Prioritize loading tiles in the center of the screen by temporarily raising the
screen space error for tiles around the edge of the screen. Screen space error returns to normal once all
the tiles in the center of the screen as determined by the <a href="Cesium3DTileset.html#foveatedConeSize"><code>Cesium3DTileset#foveatedConeSize</code></a> are loaded.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">true</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="foveatedTimeDelay">
    <a href="#foveatedTimeDelay" class="doc-link"></a>
    foveatedTimeDelay<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L469">engine/Source/Scene/Cesium3DTileset.js 469</a>
</div>


</h4>

</div>



<div class="description">
    Optimization option. Used when <a href="Cesium3DTileset.html#foveatedScreenSpaceError"><code>Cesium3DTileset#foveatedScreenSpaceError</code></a> is true to control
how long in seconds to wait after the camera stops moving before deferred tiles start loading in.
This time delay prevents requesting tiles around the edges of the screen when the camera is moving.
Setting this to 0.0 will immediately request all tiles in any given view.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">0.2</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="heightReference">
    <a href="#heightReference" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> heightReference<span class="type-signature"> : <a href="global.html#HeightReference">HeightReference</a>|undefined</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1829">engine/Source/Scene/Cesium3DTileset.js 1829</a>
</div>


</h4>

</div>



<div class="description">
    Specifies if the height is relative to terrain, 3D Tiles, or both.
<p>
This option is only applied to point features in tilesets containing vector data.
This option requires the Viewer's scene to be passed in through options.scene.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">undefined</code>
    

    

    

    

    
    <div class="tag-experimental">
        <h5>Experimental</h5>
        
            <p>This feature is using part of the 3D Tiles spec that is not final and is subject to change without Cesium's standard deprecation policy.</p>
        
    </div>
    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="imageBasedLighting">
    <a href="#imageBasedLighting" class="doc-link"></a>
    imageBasedLighting<span class="type-signature"> : <a href="ImageBasedLighting.html">ImageBasedLighting</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1944">engine/Source/Scene/Cesium3DTileset.js 1944</a>
</div>


</h4>

</div>



<div class="description">
    The properties for managing image-based lighting on this tileset.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="imageryLayers">
    <a href="#imageryLayers" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> imageryLayers<span class="type-signature"> : <a href="ImageryLayerCollection.html">ImageryLayerCollection</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1181">engine/Source/Scene/Cesium3DTileset.js 1181</a>
</div>


</h4>

</div>



<div class="description">
    The collection of <code>ImageryLayer</code> objects providing 2D georeferenced
image data that will be rendered over the tileset.

The imagery will be draped over glTF, B3DM, PNTS, or GeoJSON tile content.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">// Drape Bing Maps Aerial imagery over the tileset
const imageryProvider = await Cesium.createWorldImageryAsync({
  style: Cesium.IonWorldImageryStyle.AERIAL,
});
const imageryLayer = new ImageryLayer(imageryProvider);
tileset.imageryLayers.add(imageryLayer);</code></pre>

    

    

    

    
    <div class="tag-experimental">
        <h5>Experimental</h5>
        
            <p>This feature is not final and is subject to change without Cesium's standard deprecation policy.</p>
        
    </div>
    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="ImageryLayer.html">ImageryLayer</a></li>
    </ul>
    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="immediatelyLoadDesiredLevelOfDetail">
    <a href="#immediatelyLoadDesiredLevelOfDetail" class="doc-link"></a>
    immediatelyLoadDesiredLevelOfDetail<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L808">engine/Source/Scene/Cesium3DTileset.js 808</a>
</div>


</h4>

</div>



<div class="description">
    When true, only tiles that meet the maximum screen space error will ever be downloaded.
Skipping factors are ignored and just the desired tiles are loaded.
<p>
Only used when <a href="Cesium3DTileset.html#skipLevelOfDetail"><code>Cesium3DTileset#skipLevelOfDetail</code></a> is <code>true</code>.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="initialTilesLoaded">
    <a href="#initialTilesLoaded" class="doc-link"></a>
    initialTilesLoaded<span class="type-signature"> : <a href="Event.html">Event</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L636">engine/Source/Scene/Cesium3DTileset.js 636</a>
</div>


</h4>

</div>



<div class="description">
    The event fired to indicate that all tiles that meet the screen space error this frame are loaded. This event
is fired once when all tiles in the initial view are loaded.
<p>
This event is fired at the end of the frame after the scene is rendered.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">new Event()</code>
    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">tileset.initialTilesLoaded.addEventListener(function() {
    console.log('Initial tiles are loaded');
});</code></pre>

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Cesium3DTileset.html#allTilesLoaded">Cesium3DTileset#allTilesLoaded</a></li>
    </ul>
    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="instanceFeatureIdLabel">
    <a href="#instanceFeatureIdLabel" class="doc-link"></a>
    instanceFeatureIdLabel<span class="type-signature"> : string</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L2089">engine/Source/Scene/Cesium3DTileset.js 2089</a>
</div>


</h4>

</div>



<div class="description">
    Label of the instance feature ID set used for picking and styling.
<p>
If instanceFeatureIdLabel is set to an integer N, it is converted to
the string "instanceFeatureId_N" automatically.
If both per-primitive and per-instance feature IDs are present, the
instance feature IDs take priority.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    
    <div class="tag-experimental">
        <h5>Experimental</h5>
        
            <p>This feature is using part of the 3D Tiles spec that is not final and is subject to change without Cesium's standard deprecation policy.</p>
        
    </div>
    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="lightColor">
    <a href="#lightColor" class="doc-link"></a>
    lightColor<span class="type-signature"> : <a href="Cartesian3.html">Cartesian3</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L864">engine/Source/Scene/Cesium3DTileset.js 864</a>
</div>


</h4>

</div>



<div class="description">
    The light color when shading models. When <code>undefined</code> the scene's light color is used instead.
<p>
For example, disabling additional light sources by setting
<code>tileset.imageBasedLighting.imageBasedLightingFactor = new Cartesian2(0.0, 0.0)</code>
will make the tileset much darker. Here, increasing the intensity of the light source will make the tileset brighter.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">undefined</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="loadProgress">
    <a href="#loadProgress" class="doc-link"></a>
    loadProgress<span class="type-signature"> : <a href="Event.html">Event</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L598">engine/Source/Scene/Cesium3DTileset.js 598</a>
</div>


</h4>

</div>



<div class="description">
    The event fired to indicate progress of loading new tiles.  This event is fired when a new tile
is requested, when a requested tile is finished downloading, and when a downloaded tile has been
processed and is ready to render.
<p>
The number of pending tile requests, <code>numberOfPendingRequests</code>, and number of tiles
processing, <code>numberOfTilesProcessing</code> are passed to the event listener.
</p>
<p>
This event is fired at the end of the frame after the scene is rendered.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">new Event()</code>
    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">tileset.loadProgress.addEventListener(function(numberOfPendingRequests, numberOfTilesProcessing) {
    if ((numberOfPendingRequests === 0) &amp;&amp; (numberOfTilesProcessing === 0)) {
        console.log('Stopped loading');
        return;
    }

    console.log(`Loading: requests: ${numberOfPendingRequests}, processing: ${numberOfTilesProcessing}`);
});</code></pre>

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="loadSiblings">
    <a href="#loadSiblings" class="doc-link"></a>
    loadSiblings<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L821">engine/Source/Scene/Cesium3DTileset.js 821</a>
</div>


</h4>

</div>



<div class="description">
    Determines whether siblings of visible tiles are always downloaded during traversal.
This may be useful for ensuring that tiles are already available when the viewer turns left/right.
<p>
Only used when <a href="Cesium3DTileset.html#skipLevelOfDetail"><code>Cesium3DTileset#skipLevelOfDetail</code></a> is <code>true</code>.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="maximumCacheOverflowBytes">
    <a href="#maximumCacheOverflowBytes" class="doc-link"></a>
    maximumCacheOverflowBytes<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1579">engine/Source/Scene/Cesium3DTileset.js 1579</a>
</div>


</h4>

</div>



<div class="description">
    The maximum additional amount of GPU memory (in bytes) that will be used to cache tiles.
<p>
If tiles sized more than <code>cacheBytes</code> plus <code>maximumCacheOverflowBytes</code>
are needed to meet the desired screen space error, determined by
<a href="Cesium3DTileset.html#maximumScreenSpaceError"><code>Cesium3DTileset#maximumScreenSpaceError</code></a> for the current view, then
<code>Cesium3DTileset#memoryAdjustedScreenSpaceError</code> will be adjusted
until the tiles required to meet the adjusted screen space error use less
than <code>cacheBytes</code> plus <code>maximumCacheOverflowBytes</code>.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">536870912</code>
    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Cesium3DTileset.html#totalMemoryUsageInBytes">Cesium3DTileset#totalMemoryUsageInBytes</a></li>
    </ul>
    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="maximumScreenSpaceError">
    <a href="#maximumScreenSpaceError" class="doc-link"></a>
    maximumScreenSpaceError<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1500">engine/Source/Scene/Cesium3DTileset.js 1500</a>
</div>


</h4>

</div>



<div class="description">
    The maximum screen space error used to drive level of detail refinement.  This value helps determine when a tile
refines to its descendants, and therefore plays a major role in balancing performance with visual quality.
<p>
A tile's screen space error is roughly equivalent to the number of pixels wide that would be drawn if a sphere with a
radius equal to the tile's <b>geometric error</b> were rendered at the tile's position. If this value exceeds
<code>maximumScreenSpaceError</code> the tile refines to its descendants.
</p>
<p>
Depending on the tileset, <code>maximumScreenSpaceError</code> may need to be tweaked to achieve the right balance.
Higher values provide better performance but lower visual quality.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">16</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="modelMatrix">
    <a href="#modelMatrix" class="doc-link"></a>
    modelMatrix<span class="type-signature"> : <a href="Matrix4.html">Matrix4</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1686">engine/Source/Scene/Cesium3DTileset.js 1686</a>
</div>


</h4>

</div>



<div class="description">
    A 4x4 transformation matrix that transforms the entire tileset.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">Matrix4.IDENTITY</code>
    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">// Adjust a tileset's height from the globe's surface.
const heightOffset = 20.0;
const boundingSphere = tileset.boundingSphere;
const cartographic = Cesium.Cartographic.fromCartesian(boundingSphere.center);
const surface = Cesium.Cartesian3.fromRadians(cartographic.longitude, cartographic.latitude, 0.0);
const offset = Cesium.Cartesian3.fromRadians(cartographic.longitude, cartographic.latitude, heightOffset);
const translation = Cesium.Cartesian3.subtract(offset, surface, new Cesium.Cartesian3());
tileset.modelMatrix = Cesium.Matrix4.fromTranslation(translation);</code></pre>

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="outlineColor">
    <a href="#outlineColor" class="doc-link"></a>
    outlineColor<span class="type-signature"> : <a href="Color.html">Color</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L893">engine/Source/Scene/Cesium3DTileset.js 893</a>
</div>


</h4>

</div>



<div class="description">
    The color to use when rendering outlines.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">Color.BLACK</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="pointCloudShading">
    <a href="#pointCloudShading" class="doc-link"></a>
    pointCloudShading<span class="type-signature"> : <a href="PointCloudShading.html">PointCloudShading</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1619">engine/Source/Scene/Cesium3DTileset.js 1619</a>
</div>


</h4>

</div>



<div class="description">
    Options for controlling point size based on geometric error and eye dome lighting.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="preferLeaves">
    <a href="#preferLeaves" class="doc-link"></a>
    preferLeaves<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L333">engine/Source/Scene/Cesium3DTileset.js 333</a>
</div>


</h4>

</div>



<div class="description">
    Optimization option. Prefer loading of leaves first.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="preloadFlightDestinations">
    <a href="#preloadFlightDestinations" class="doc-link"></a>
    preloadFlightDestinations<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L423">engine/Source/Scene/Cesium3DTileset.js 423</a>
</div>


</h4>

</div>



<div class="description">
    Optimization option. Fetch tiles at the camera's flight destination while the camera is in flight.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">true</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="preloadWhenHidden">
    <a href="#preloadWhenHidden" class="doc-link"></a>
    preloadWhenHidden<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L415">engine/Source/Scene/Cesium3DTileset.js 415</a>
</div>


</h4>

</div>



<div class="description">
    Preload tiles when <code>tileset.show</code> is <code>false</code>. Loads tiles as if the tileset is visible but does not render them.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="progressiveResolutionHeightFraction">
    <a href="#progressiveResolutionHeightFraction" class="doc-link"></a>
    progressiveResolutionHeightFraction<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L321">engine/Source/Scene/Cesium3DTileset.js 321</a>
</div>


</h4>

</div>



<div class="description">
    Optimization option. If between (0.0, 0.5], tiles at or above the screen space error for the reduced screen resolution of <code>progressiveResolutionHeightFraction*screenHeight</code> will be prioritized first. This can help get a quick layer of tiles down while full resolution tiles continue to load.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">0.3</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="properties">
    <a href="#properties" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> properties<span class="type-signature"> : object</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1241">engine/Source/Scene/Cesium3DTileset.js 1241</a>
</div>


</h4>

</div>



<div class="description">
    Gets the tileset's properties dictionary object, which contains metadata about per-feature properties.
<p>
See the <a href="https://github.com/CesiumGS/3d-tiles/tree/main/specification#reference-properties">properties schema reference</a>
in the 3D Tiles spec for the full set of properties.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">console.log(`Maximum building height: ${tileset.properties.height.maximum}`);
console.log(`Minimum building height: ${tileset.properties.height.minimum}`);</code></pre>

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Cesium3DTileFeature.html#getProperty">Cesium3DTileFeature#getProperty</a></li>
    
        <li><a href="Cesium3DTileFeature.html#setProperty">Cesium3DTileFeature#setProperty</a></li>
    </ul>
    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="resource">
    <a href="#resource" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> resource<span class="type-signature"> : <a href="Resource.html">Resource</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1274">engine/Source/Scene/Cesium3DTileset.js 1274</a>
</div>


</h4>

</div>



<div class="description">
    The resource used to fetch the tileset JSON file
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="root">
    <a href="#root" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> root<span class="type-signature"> : <a href="Cesium3DTile.html">Cesium3DTile</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1639">engine/Source/Scene/Cesium3DTileset.js 1639</a>
</div>


</h4>

</div>



<div class="description">
    The root tile.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="shadows">
    <a href="#shadows" class="doc-link"></a>
    shadows<span class="type-signature"> : <a href="global.html#ShadowMode">ShadowMode</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L541">engine/Source/Scene/Cesium3DTileset.js 541</a>
</div>


</h4>

</div>



<div class="description">
    Determines whether the tileset casts or receives shadows from light sources.
<p>
Enabling shadows has a performance impact. A tileset that casts shadows must be rendered twice, once from the camera and again from the light's point of view.
</p>
<p>
Shadows are rendered only when <a href="Viewer.html#shadows"><code>Viewer#shadows</code></a> is <code>true</code>.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">ShadowMode.ENABLED</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="show">
    <a href="#show" class="doc-link"></a>
    show<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L549">engine/Source/Scene/Cesium3DTileset.js 549</a>
</div>


</h4>

</div>



<div class="description">
    Determines if the tileset will be shown.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">true</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="showCreditsOnScreen">
    <a href="#showCreditsOnScreen" class="doc-link"></a>
    showCreditsOnScreen<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L2025">engine/Source/Scene/Cesium3DTileset.js 2025</a>
</div>


</h4>

</div>



<div class="description">
    Determines whether the credits of the tileset will be displayed on the screen
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="showOutline">
    <a href="#showOutline" class="doc-link"></a>
    showOutline<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L885">engine/Source/Scene/Cesium3DTileset.js 885</a>
</div>


</h4>

</div>



<div class="description">
    Whether to display the outline for models using the
<a href="https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/CESIUM_primitive_outline">CESIUM_primitive_outline</a> extension.
When true, outlines are displayed. When false, outlines are not displayed.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">true</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="skipLevelOfDetail">
    <a href="#skipLevelOfDetail" class="doc-link"></a>
    skipLevelOfDetail<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L758">engine/Source/Scene/Cesium3DTileset.js 758</a>
</div>


</h4>

</div>



<div class="description">
    Optimization option. Determines if level of detail skipping should be applied during the traversal.
<p>
The common strategy for replacement-refinement traversal is to store all levels of the tree in memory and require
all children to be loaded before the parent can refine. With this optimization levels of the tree can be skipped
entirely and children can be rendered alongside their parents. The tileset requires significantly less memory when
using this optimization.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="skipLevels">
    <a href="#skipLevels" class="doc-link"></a>
    skipLevels<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L796">engine/Source/Scene/Cesium3DTileset.js 796</a>
</div>


</h4>

</div>



<div class="description">
    Constant defining the minimum number of levels to skip when loading tiles. When it is 0, no levels are skipped.
For example, if a tile is level 1, no tiles will be loaded unless they are at level greater than 2.
<p>
Only used when <a href="Cesium3DTileset.html#skipLevelOfDetail"><code>Cesium3DTileset#skipLevelOfDetail</code></a> is <code>true</code>.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">1</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="skipScreenSpaceErrorFactor">
    <a href="#skipScreenSpaceErrorFactor" class="doc-link"></a>
    skipScreenSpaceErrorFactor<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L784">engine/Source/Scene/Cesium3DTileset.js 784</a>
</div>


</h4>

</div>



<div class="description">
    Multiplier defining the minimum screen space error to skip.
For example, if a tile has screen space error of 100, no tiles will be loaded unless they
are leaves or have a screen space error <code><= 100 / skipScreenSpaceErrorFactor</code>.
<p>
Only used when <a href="Cesium3DTileset.html#skipLevelOfDetail"><code>Cesium3DTileset#skipLevelOfDetail</code></a> is <code>true</code>.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">16</code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="splitDirection">
    <a href="#splitDirection" class="doc-link"></a>
    splitDirection<span class="type-signature"> : <a href="global.html#SplitDirection">SplitDirection</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L901">engine/Source/Scene/Cesium3DTileset.js 901</a>
</div>


</h4>

</div>



<div class="description">
    The <a href="global.html#SplitDirection"><code>SplitDirection</code></a> to apply to this tileset.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript"><a href="global.html#SplitDirection#.NONE"><code>SplitDirection.NONE</code></a></code>
    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="style">
    <a href="#style" class="doc-link"></a>
    style<span class="type-signature"> : <a href="Cesium3DTileStyle.html">Cesium3DTileStyle</a>|undefined</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1344">engine/Source/Scene/Cesium3DTileset.js 1344</a>
</div>


</h4>

</div>



<div class="description">
    The style, defined using the
<a href="https://github.com/CesiumGS/3d-tiles/tree/main/specification/Styling">3D Tiles Styling language</a>,
applied to each feature in the tileset.
<p>
Assign <code>undefined</code> to remove the style, which will restore the visual
appearance of the tileset to its default when no style was applied.
</p>
<p>
The style is applied to a tile before the <a href="Cesium3DTileset.html#tileVisible"><code>Cesium3DTileset#tileVisible</code></a>
event is raised, so code in <code>tileVisible</code> can manually set a feature's
properties (e.g. color and show) after the style is applied. When
a new style is assigned any manually set properties are overwritten.
</p>
<p>
Use an always "true" condition to specify the Color for all objects that are not
overridden by pre-existing conditions. Otherwise, the default color Cesium.Color.White
will be used. Similarly, use an always "true" condition to specify the show property
for all objects that are not overridden by pre-existing conditions. Otherwise, the
default show value true will be used.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">undefined</code>
    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">tileset.style = new Cesium.Cesium3DTileStyle({
   color : {
       conditions : [
           ['${Height} >= 100', 'color("purple", 0.5)'],
           ['${Height} >= 50', 'color("red")'],
           ['true', 'color("blue")']
       ]
   },
   show : '${Height} > 0',
   meta : {
       description : '"Building id ${id} has height ${Height}."'
   }
});</code></pre>

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="https://github.com/CesiumGS/3d-tiles/tree/main/specification/Styling">3D Tiles Styling language</a></li>
    </ul>
    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="tileFailed">
    <a href="#tileFailed" class="doc-link"></a>
    tileFailed<span class="type-signature"> : <a href="Event.html">Event</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L707">engine/Source/Scene/Cesium3DTileset.js 707</a>
</div>


</h4>

</div>



<div class="description">
    The event fired to indicate that a tile's content failed to load.
<p>
If there are no event listeners, error messages will be logged to the console.
</p>
<p>
The error object passed to the listener contains two properties:
<ul>
<li><code>url</code>: the url of the failed tile.</li>
<li><code>message</code>: the error message.</li>
</ul>
<p>
If multiple contents are present, this event is raised once per inner content with errors.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">new Event()</code>
    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">tileset.tileFailed.addEventListener(function(error) {
    console.log(`An error occurred loading tile: ${error.url}`);
    console.log(`Error: ${error.message}`);
});</code></pre>

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="tileLoad">
    <a href="#tileLoad" class="doc-link"></a>
    tileLoad<span class="type-signature"> : <a href="Event.html">Event</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L657">engine/Source/Scene/Cesium3DTileset.js 657</a>
</div>


</h4>

</div>



<div class="description">
    The event fired to indicate that a tile's content was loaded.
<p>
The loaded <a href="Cesium3DTile.html"><code>Cesium3DTile</code></a> is passed to the event listener.
</p>
<p>
This event is fired during the tileset traversal while the frame is being rendered
so that updates to the tile take effect in the same frame.  Do not create or modify
Cesium entities or primitives during the event listener.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">new Event()</code>
    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">tileset.tileLoad.addEventListener(function(tile) {
    console.log('A tile was loaded.');
});</code></pre>

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="tilesLoaded">
    <a href="#tilesLoaded" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> tilesLoaded<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1260">engine/Source/Scene/Cesium3DTileset.js 1260</a>
</div>


</h4>

</div>



<div class="description">
    When <code>true</code>, all tiles that meet the screen space error this frame are loaded. The tileset is
completely loaded for this view.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Cesium3DTileset.html#allTilesLoaded">Cesium3DTileset#allTilesLoaded</a></li>
    </ul>
    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="tileUnload">
    <a href="#tileUnload" class="doc-link"></a>
    tileUnload<span class="type-signature"> : <a href="Event.html">Event</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L681">engine/Source/Scene/Cesium3DTileset.js 681</a>
</div>


</h4>

</div>



<div class="description">
    The event fired to indicate that a tile's content was unloaded.
<p>
The unloaded <a href="Cesium3DTile.html"><code>Cesium3DTile</code></a> is passed to the event listener.
</p>
<p>
This event is fired immediately before the tile's content is unloaded while the frame is being
rendered so that the event listener has access to the tile's content.  Do not create
or modify Cesium entities or primitives during the event listener.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">new Event()</code>
    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">tileset.tileUnload.addEventListener(function(tile) {
    console.log('A tile was unloaded from the cache.');
});</code></pre>

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Cesium3DTileset.html#cacheBytes">Cesium3DTileset#cacheBytes</a></li>
    
        <li><a href="Cesium3DTileset.html#trimLoadedTiles">Cesium3DTileset#trimLoadedTiles</a></li>
    </ul>
    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="tileVisible">
    <a href="#tileVisible" class="doc-link"></a>
    tileVisible<span class="type-signature"> : <a href="Event.html">Event</a></span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L744">engine/Source/Scene/Cesium3DTileset.js 744</a>
</div>


</h4>

</div>



<div class="description">
    This event fires once for each visible tile in a frame.  This can be used to manually
style a tileset.
<p>
The visible <a href="Cesium3DTile.html"><code>Cesium3DTile</code></a> is passed to the event listener.
</p>
<p>
This event is fired during the tileset traversal while the frame is being rendered
so that updates to the tile take effect in the same frame.  Do not create or modify
Cesium entities or primitives during the event listener.
</p>
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">new Event()</code>
    

    
        <h5>Examples:</h5>
        
    <pre><code class="language-javascript">tileset.tileVisible.addEventListener(function(tile) {
    if (tile.content instanceof Cesium.Model3DTileContent) {
        console.log('A 3D model tile is visible.');
    }
});</code></pre>

    <pre><code class="language-javascript">// Apply a red style and then manually set random colors for every other feature when the tile becomes visible.
tileset.style = new Cesium.Cesium3DTileStyle({
    color : 'color("red")'
});
tileset.tileVisible.addEventListener(function(tile) {
    const content = tile.content;
    const featuresLength = content.featuresLength;
    for (let i = 0; i &lt; featuresLength; i+=2) {
        content.getFeature(i).color = Cesium.Color.fromRandom();
    }
});</code></pre>

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="timeSinceLoad">
    <a href="#timeSinceLoad" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> timeSinceLoad<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1703">engine/Source/Scene/Cesium3DTileset.js 1703</a>
</div>


</h4>

</div>



<div class="description">
    Returns the time, in milliseconds, since the tileset was loaded and first updated.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="totalMemoryUsageInBytes">
    <a href="#totalMemoryUsageInBytes" class="doc-link"></a>
    <span class="type-signature attribute-readonly">readonly</span> totalMemoryUsageInBytes<span class="type-signature"> : number</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1720">engine/Source/Scene/Cesium3DTileset.js 1720</a>
</div>


</h4>

</div>



<div class="description">
    The total amount of GPU memory in bytes used by the tileset. This value is estimated from
geometry, texture, batch table textures, and binary metadata of loaded tiles.
</div>





<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Cesium3DTileset.html#cacheBytes">Cesium3DTileset#cacheBytes</a></li>
    </ul>
    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="vectorClassificationOnly">
    <a href="#vectorClassificationOnly" class="doc-link"></a>
    vectorClassificationOnly<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L1994">engine/Source/Scene/Cesium3DTileset.js 1994</a>
</div>


</h4>

</div>



<div class="description">
    Indicates that only the tileset's vector tiles should be used for classification.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    
    <div class="tag-experimental">
        <h5>Experimental</h5>
        
            <p>This feature is using part of the 3D Tiles spec that is not final and is subject to change without Cesium's standard deprecation policy.</p>
        
    </div>
    

    

    
</dl>


        
            
<div class="nameContainer">
<h4 class="name" id="vectorKeepDecodedPositions">
    <a href="#vectorKeepDecodedPositions" class="doc-link"></a>
    vectorKeepDecodedPositions<span class="type-signature"> : boolean</span>
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L2011">engine/Source/Scene/Cesium3DTileset.js 2011</a>
</div>


</h4>

</div>



<div class="description">
    Whether vector tiles should keep decoded positions in memory.
This is used with <code>Cesium3DTileFeature.getPolylinePositions</code>.
</div>





<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">false</code>
    

    

    

    

    
    <div class="tag-experimental">
        <h5>Experimental</h5>
        
            <p>This feature is using part of the 3D Tiles spec that is not final and is subject to change without Cesium's standard deprecation policy.</p>
        
    </div>
    

    

    
</dl>


        
    

    
        <h3 class="subsection-title">Methods</h3>

        
            
    <div class="nameContainer">
    <h4 class="name" id=".fromIonAssetId">
        <a href="#.fromIonAssetId" class="doc-link"></a>
        <span class="type-signature attribute-static">static</span> Cesium.Cesium3DTileset.fromIonAssetId<span class="signature">(assetId, <span class="optional">options</span>)</span> &rarr; <span class="type-signature returnType">Promise.&lt;<a href="Cesium3DTileset.html">Cesium3DTileset</a>></span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L2130">engine/Source/Scene/Cesium3DTileset.js 2130</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Creates a <a href="https://github.com/CesiumGS/3d-tiles/tree/main/specification">3D Tiles tileset</a>,
used for streaming massive heterogeneous 3D geospatial datasets, from a Cesium ion asset ID.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>assetId</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The Cesium ion asset id.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cesium3DTileset.html#.ConstructorOptions">Cesium3DTileset.ConstructorOptions</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                An object describing initialization options</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        

    


<h5>Throws:</h5>
<ul>
        <li>

    <div class="param-desc">
        
<span class="param-type"><a href="RuntimeError.html">RuntimeError</a></span>

: When the tileset asset version is not 0.0, 1.0, or 1.1,
or when the tileset contains a required extension that is not supported.
    </div>

</li>
    </ul>



<dl class="details">


    

    

    

    

    

    

    

    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">// Load a Cesium3DTileset with a Cesium ion asset ID of 124624234
try {
  const tileset = await Cesium.Cesium3DTileset.fromIonAssetId(124624234);
  scene.primitives.add(tileset);
} catch (error) {
  console.error(`Error creating tileset: ${error}`);
}</code></pre>

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li>Cesium3DTileset#fromUrl</li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id=".fromUrl">
        <a href="#.fromUrl" class="doc-link"></a>
        <span class="type-signature attribute-static">static</span> Cesium.Cesium3DTileset.fromUrl<span class="signature">(url, <span class="optional">options</span>)</span> &rarr; <span class="type-signature returnType">Promise.&lt;<a href="Cesium3DTileset.html">Cesium3DTileset</a>></span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L2187">engine/Source/Scene/Cesium3DTileset.js 2187</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Creates a <a href="https://github.com/CesiumGS/3d-tiles/tree/main/specification">3D Tiles tileset</a>,
used for streaming massive heterogeneous 3D geospatial datasets.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>url</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Resource.html">Resource</a></span>
|

<span class="param-type">string</span>


            
            </td>

            

            <td class="description last">
            
                
                

                
            
                The url to a tileset JSON file.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cesium3DTileset.html#.ConstructorOptions">Cesium3DTileset.ConstructorOptions</a></span>


            
            </td>

            

            <td class="description last">
            
                
                        <span class="optional">optional</span>
                
                

                
            
                An object describing initialization options</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        

    


<h5>Throws:</h5>
<ul>
        <li>

    <div class="param-desc">
        
<span class="param-type"><a href="RuntimeError.html">RuntimeError</a></span>

: When the tileset asset version is not 0.0, 1.0, or 1.1,
or when the tileset contains a required extension that is not supported.
    </div>

</li>
    </ul>



<dl class="details">


    

    

    

    

    

    

    

    

    
        <h5>Examples:</h5>
        
    <pre><code class="language-javascript">try {
  const tileset = await Cesium.Cesium3DTileset.fromUrl(
     "http://localhost:8002/tilesets/Seattle/tileset.json"
  );
  scene.primitives.add(tileset);
} catch (error) {
  console.error(`Error creating tileset: ${error}`);
}</code></pre>

    <pre><code class="language-javascript">// Common setting for the skipLevelOfDetail optimization
const tileset = await Cesium.Cesium3DTileset.fromUrl(
  "http://localhost:8002/tilesets/Seattle/tileset.json", {
     skipLevelOfDetail: true,
     baseScreenSpaceError: 1024,
     skipScreenSpaceErrorFactor: 16,
     skipLevels: 1,
     immediatelyLoadDesiredLevelOfDetail: false,
     loadSiblings: false,
     cullWithChildrenBounds: true
});
scene.primitives.add(tileset);</code></pre>

    <pre><code class="language-javascript">// Common settings for the dynamicScreenSpaceError optimization
const tileset = await Cesium.Cesium3DTileset.fromUrl(
  "http://localhost:8002/tilesets/Seattle/tileset.json", {
     dynamicScreenSpaceError: true,
     dynamicScreenSpaceErrorDensity: 2.0e-4,
     dynamicScreenSpaceErrorFactor: 24.0,
     dynamicScreenSpaceErrorHeightFalloff: 0.25
});
scene.primitives.add(tileset);</code></pre>

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li>Cesium3DTileset#fromIonAssetId</li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id=".loadJson">
        <a href="#.loadJson" class="doc-link"></a>
        <span class="type-signature attribute-static">static</span> Cesium.Cesium3DTileset.loadJson<span class="signature">(tilesetUrl)</span> &rarr; <span class="type-signature returnType">Promise.&lt;object></span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L2273">engine/Source/Scene/Cesium3DTileset.js 2273</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Provides a hook to override the method used to request the tileset json
useful when fetching tilesets from remote servers
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>tilesetUrl</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Resource.html">Resource</a></span>
|

<span class="param-type">string</span>


            
            </td>

            

            <td class="description last">
            
                The url of the json file to be fetched</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    A promise that resolves with the fetched json data
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="destroy">
        <a href="#destroy" class="doc-link"></a>
        destroy<span class="signature">()</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L3612">engine/Source/Scene/Cesium3DTileset.js 3612</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Destroys the WebGL resources held by this object.  Destroying an object allows for deterministic
release of WebGL resources, instead of relying on the garbage collector to destroy this object.
<br /><br />
Once an object is destroyed, it should not be used; calling any function other than
<code>isDestroyed</code> will result in a <a href="DeveloperError.html"><code>DeveloperError</code></a> exception.  Therefore,
assign the return value (<code>undefined</code>) to the object as done in the example.
</div>























<h5>Throws:</h5>
<ul>
        <li>

    <div class="param-desc">
        
<span class="param-type"><a href="DeveloperError.html">DeveloperError</a></span>

: This object was destroyed, i.e., destroy() was called.
    </div>

</li>
    </ul>



<dl class="details">


    

    

    

    

    

    

    

    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">tileset = tileset &amp;&amp; tileset.destroy();</code></pre>

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Cesium3DTileset.html#isDestroyed">Cesium3DTileset#isDestroyed</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="getHeight">
        <a href="#getHeight" class="doc-link"></a>
        getHeight<span class="signature">(cartographic, scene)</span> &rarr; <span class="type-signature returnType">number|undefined</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L3715">engine/Source/Scene/Cesium3DTileset.js 3715</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Get the height of the loaded surface at a given cartographic. This function will only take into account meshes for loaded tiles, not neccisarily the most detailed tiles available for a tileset. This function will always return undefined when sampling a point cloud.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>cartographic</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartographic.html">Cartographic</a></span>


            
            </td>

            

            <td class="description last">
            
                The cartographic for which to find the height.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>scene</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Scene.html">Scene</a></span>


            
            </td>

            

            <td class="description last">
            
                The scene where visualization is taking place.</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    The height of the cartographic or undefined if it could not be found.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    
        <h5>Example:</h5>
        
    <pre><code class="language-javascript">const tileset = await Cesium.Cesium3DTileset.fromIonAssetId(124624234);
scene.primitives.add(tileset);

const height = tileset.getHeight(scene.camera.positionCartographic, scene);</code></pre>

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="hasExtension">
        <a href="#hasExtension" class="doc-link"></a>
        hasExtension<span class="signature">(extensionName)</span> &rarr; <span class="type-signature returnType">boolean</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L3575">engine/Source/Scene/Cesium3DTileset.js 3575</a>
</div>


    </h4>

    </div>

    


<div class="description">
    <code>true</code> if the tileset JSON file lists the extension in extensionsUsed; otherwise, <code>false</code>.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>extensionName</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            

            <td class="description last">
            
                The name of the extension to check.</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    <code>true</code> if the tileset JSON file lists the extension in extensionsUsed; otherwise, <code>false</code>.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="isDestroyed">
        <a href="#isDestroyed" class="doc-link"></a>
        isDestroyed<span class="signature">()</span> &rarr; <span class="type-signature returnType">boolean</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L3593">engine/Source/Scene/Cesium3DTileset.js 3593</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Returns true if this object was destroyed; otherwise, false.
<br /><br />
If this object was destroyed, it should not be used; calling any function other than
<code>isDestroyed</code> will result in a <a href="DeveloperError.html"><code>DeveloperError</code></a> exception.
</div>





















<h5>Returns:</h5>

        
<div class="param-desc">
    <code>true</code> if this object was destroyed; otherwise, <code>false</code>.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    
    <h5>See:</h5>
    <ul class="see-list">
        <li><a href="Cesium3DTileset.html#destroy">Cesium3DTileset#destroy</a></li>
    </ul>
    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="makeStyleDirty">
        <a href="#makeStyleDirty" class="doc-link"></a>
        makeStyleDirty<span class="signature">()</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L2282">engine/Source/Scene/Cesium3DTileset.js 2282</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Marks the tileset's <a href="Cesium3DTileset.html#style"><code>Cesium3DTileset#style</code></a> as dirty, which forces all
features to re-evaluate the style in the next frame each is visible.
</div>

























<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
            
    <div class="nameContainer">
    <h4 class="name" id="trimLoadedTiles">
        <a href="#trimLoadedTiles" class="doc-link"></a>
        trimLoadedTiles<span class="signature">()</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L3270">engine/Source/Scene/Cesium3DTileset.js 3270</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Unloads all tiles that weren't selected the previous frame.  This can be used to
explicitly manage the tile cache and reduce the total number of tiles loaded below
<a href="Cesium3DTileset.html#cacheBytes"><code>Cesium3DTileset#cacheBytes</code></a>.
<p>
Tile unloads occur at the next frame to keep all the WebGL delete calls
within the render loop.
</p>
</div>

























<dl class="details">


    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
    

    
        <h3 class="subsection-title">Type Definitions</h3>

        
                
<div class="nameContainer">
<h4 class="name" id=".ConstructorOptions">
    <a href="#.ConstructorOptions" class="doc-link"></a>
    Cesium.Cesium3DTileset.ConstructorOptions
    

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L67">engine/Source/Scene/Cesium3DTileset.js 67</a>
</div>


</h4>

</div>



<div class="description">
    Initialization options for the Cesium3DTileset constructor
</div>





<dl class="details">


    <h5 class="subsection-title">Properties:</h5>

    

<table class="props">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>show</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    true
                
                </td>
            

            <td class="description last">Determines if the tileset will be shown.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>modelMatrix</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Matrix4.html">Matrix4</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    Matrix4.IDENTITY
                
                </td>
            

            <td class="description last">A 4x4 transformation matrix that transforms the tileset's root tile.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>modelUpAxis</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#Axis">Axis</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    Axis.Y
                
                </td>
            

            <td class="description last">Which axis is considered up when loading models for tile contents.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>modelForwardAxis</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#Axis">Axis</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    Axis.X
                
                </td>
            

            <td class="description last">Which axis is considered forward when loading models for tile contents.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>shadows</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#ShadowMode">ShadowMode</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    ShadowMode.ENABLED
                
                </td>
            

            <td class="description last">Determines whether the tileset casts or receives shadows from light sources.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>maximumScreenSpaceError</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    16
                
                </td>
            

            <td class="description last">The maximum screen space error used to drive level of detail refinement.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>cacheBytes</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    536870912
                
                </td>
            

            <td class="description last">The size (in bytes) to which the tile cache will be trimmed, if the cache contains tiles not needed for the current view.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>maximumCacheOverflowBytes</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    536870912
                
                </td>
            

            <td class="description last">The maximum additional memory (in bytes) to allow for cache headroom, if more than <a href="Cesium3DTileset.html#cacheBytes"><code>Cesium3DTileset#cacheBytes</code></a> are needed for the current view.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>cullWithChildrenBounds</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    true
                
                </td>
            

            <td class="description last">Optimization option. Whether to cull tiles using the union of their children bounding volumes.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>cullRequestsWhileMoving</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    true
                
                </td>
            

            <td class="description last">Optimization option. Don't request tiles that will likely be unused when they come back because of the camera's movement. This optimization only applies to stationary tilesets.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>cullRequestsWhileMovingMultiplier</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    60.0
                
                </td>
            

            <td class="description last">Optimization option. Multiplier used in culling requests while moving. Larger is more aggressive culling, smaller less aggressive culling.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>preloadWhenHidden</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">Preload tiles when <code>tileset.show</code> is <code>false</code>. Loads tiles as if the tileset is visible but does not render them.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>preloadFlightDestinations</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    true
                
                </td>
            

            <td class="description last">Optimization option. Preload tiles at the camera's flight destination while the camera is in flight.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>preferLeaves</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">Optimization option. Prefer loading of leaves first.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dynamicScreenSpaceError</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    true
                
                </td>
            

            <td class="description last">Optimization option. For street-level horizon views, use lower resolution tiles far from the camera. This reduces the amount of data loaded and improves tileset loading time with a slight drop in visual quality in the distance.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dynamicScreenSpaceErrorDensity</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    2.0e-4
                
                </td>
            

            <td class="description last">Similar to <a href="Fog.html#density"><code>Fog#density</code></a>, this option controls the camera distance at which the <a href="Cesium3DTileset.html#dynamicScreenSpaceError"><code>Cesium3DTileset#dynamicScreenSpaceError</code></a> optimization applies. Larger values will cause tiles closer to the camera to be affected.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dynamicScreenSpaceErrorFactor</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    24.0
                
                </td>
            

            <td class="description last">A parameter that controls the intensity of the <a href="Cesium3DTileset.html#dynamicScreenSpaceError"><code>Cesium3DTileset#dynamicScreenSpaceError</code></a> optimization for tiles on the horizon. Larger values cause lower resolution tiles to load, improving runtime performance at a slight reduction of visual quality.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dynamicScreenSpaceErrorHeightFalloff</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    0.25
                
                </td>
            

            <td class="description last">A ratio of the tileset's height that determines where "street level" camera views occur. When the camera is below this height, the <a href="Cesium3DTileset.html#dynamicScreenSpaceError"><code>Cesium3DTileset#dynamicScreenSpaceError</code></a> optimization will have the maximum effect, and it will roll off above this value.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>progressiveResolutionHeightFraction</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    0.3
                
                </td>
            

            <td class="description last">Optimization option. If between (0.0, 0.5], tiles at or above the screen space error for the reduced screen resolution of <code>progressiveResolutionHeightFraction*screenHeight</code> will be prioritized first. This can help get a quick layer of tiles down while full resolution tiles continue to load.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>foveatedScreenSpaceError</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    true
                
                </td>
            

            <td class="description last">Optimization option. Prioritize loading tiles in the center of the screen by temporarily raising the screen space error for tiles around the edge of the screen. Screen space error returns to normal once all the tiles in the center of the screen as determined by the <a href="Cesium3DTileset.html#foveatedConeSize"><code>Cesium3DTileset#foveatedConeSize</code></a> are loaded.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>foveatedConeSize</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    0.1
                
                </td>
            

            <td class="description last">Optimization option. Used when <a href="Cesium3DTileset.html#foveatedScreenSpaceError"><code>Cesium3DTileset#foveatedScreenSpaceError</code></a> is true to control the cone size that determines which tiles are deferred. Tiles that are inside this cone are loaded immediately. Tiles outside the cone are potentially deferred based on how far outside the cone they are and their screen space error. This is controlled by <a href="Cesium3DTileset.html#foveatedInterpolationCallback"><code>Cesium3DTileset#foveatedInterpolationCallback</code></a> and <a href="Cesium3DTileset.html#foveatedMinimumScreenSpaceErrorRelaxation"><code>Cesium3DTileset#foveatedMinimumScreenSpaceErrorRelaxation</code></a>. Setting this to 0.0 means the cone will be the line formed by the camera position and its view direction. Setting this to 1.0 means the cone encompasses the entire field of view of the camera, disabling the effect.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>foveatedMinimumScreenSpaceErrorRelaxation</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    0.0
                
                </td>
            

            <td class="description last">Optimization option. Used when <a href="Cesium3DTileset.html#foveatedScreenSpaceError"><code>Cesium3DTileset#foveatedScreenSpaceError</code></a> is true to control the starting screen space error relaxation for tiles outside the foveated cone. The screen space error will be raised starting with tileset value up to <a href="Cesium3DTileset.html#maximumScreenSpaceError"><code>Cesium3DTileset#maximumScreenSpaceError</code></a> based on the provided <a href="Cesium3DTileset.html#foveatedInterpolationCallback"><code>Cesium3DTileset#foveatedInterpolationCallback</code></a>.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>foveatedInterpolationCallback</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cesium3DTileset.html#.foveatedInterpolationCallback">Cesium3DTileset.foveatedInterpolationCallback</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    Math.lerp
                
                </td>
            

            <td class="description last">Optimization option. Used when <a href="Cesium3DTileset.html#foveatedScreenSpaceError"><code>Cesium3DTileset#foveatedScreenSpaceError</code></a> is true to control how much to raise the screen space error for tiles outside the foveated cone, interpolating between <a href="Cesium3DTileset.html#foveatedMinimumScreenSpaceErrorRelaxation"><code>Cesium3DTileset#foveatedMinimumScreenSpaceErrorRelaxation</code></a> and <a href="Cesium3DTileset.html#maximumScreenSpaceError"><code>Cesium3DTileset#maximumScreenSpaceError</code></a></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>foveatedTimeDelay</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    0.2
                
                </td>
            

            <td class="description last">Optimization option. Used when <a href="Cesium3DTileset.html#foveatedScreenSpaceError"><code>Cesium3DTileset#foveatedScreenSpaceError</code></a> is true to control how long in seconds to wait after the camera stops moving before deferred tiles start loading in. This time delay prevents requesting tiles around the edges of the screen when the camera is moving. Setting this to 0.0 will immediately request all tiles in any given view.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>skipLevelOfDetail</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">Optimization option. Determines if level of detail skipping should be applied during the traversal.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>baseScreenSpaceError</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    1024
                
                </td>
            

            <td class="description last">When <code>skipLevelOfDetail</code> is <code>true</code>, the screen space error that must be reached before skipping levels of detail.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>skipScreenSpaceErrorFactor</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    16
                
                </td>
            

            <td class="description last">When <code>skipLevelOfDetail</code> is <code>true</code>, a multiplier defining the minimum screen space error to skip. Used in conjunction with <code>skipLevels</code> to determine which tiles to load.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>skipLevels</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    1
                
                </td>
            

            <td class="description last">When <code>skipLevelOfDetail</code> is <code>true</code>, a constant defining the minimum number of levels to skip when loading tiles. When it is 0, no levels are skipped. Used in conjunction with <code>skipScreenSpaceErrorFactor</code> to determine which tiles to load.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>immediatelyLoadDesiredLevelOfDetail</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">When <code>skipLevelOfDetail</code> is <code>true</code>, only tiles that meet the maximum screen space error will ever be downloaded. Skipping factors are ignored and just the desired tiles are loaded.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>loadSiblings</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">When <code>skipLevelOfDetail</code> is <code>true</code>, determines whether siblings of visible tiles are always downloaded during traversal.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>clippingPlanes</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="ClippingPlaneCollection.html">ClippingPlaneCollection</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last">The <a href="ClippingPlaneCollection.html"><code>ClippingPlaneCollection</code></a> used to selectively disable rendering the tileset.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>clippingPolygons</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="ClippingPolygonCollection.html">ClippingPolygonCollection</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last">The <a href="ClippingPolygonCollection.html"><code>ClippingPolygonCollection</code></a> used to selectively disable rendering the tileset.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>classificationType</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#ClassificationType">ClassificationType</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last">Determines whether terrain, 3D Tiles or both will be classified by this tileset. See <a href="Cesium3DTileset.html#classificationType"><code>Cesium3DTileset#classificationType</code></a> for details about restrictions and limitations.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>heightReference</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#HeightReference">HeightReference</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last">Sets the <a href="global.html#HeightReference"><code>HeightReference</code></a> for point features in vector tilesets.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>scene</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Scene.html">Scene</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last">The <a href="CesiumWidget.html#scene"><code>CesiumWidget#scene</code></a> that the tileset will be rendered in, required for tilesets that specify a <code>heightReference</code> value for clamping 3D Tiles vector data content- like points, lines, and labels- to terrain or 3D tiles.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>ellipsoid</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Ellipsoid.html">Ellipsoid</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    Ellipsoid.WGS84
                
                </td>
            

            <td class="description last">The ellipsoid determining the size and shape of the globe.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>pointCloudShading</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last">Options for constructing a <a href="PointCloudShading.html"><code>PointCloudShading</code></a> object to control point attenuation based on geometric error and lighting.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lightColor</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Cartesian3.html">Cartesian3</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last">The light color when shading models. When <code>undefined</code> the scene's light color is used instead.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>imageBasedLighting</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="ImageBasedLighting.html">ImageBasedLighting</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last">The properties for managing image-based lighting for this tileset.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>environmentMapOptions</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="DynamicEnvironmentMapManager.html#.ConstructorOptions">DynamicEnvironmentMapManager.ConstructorOptions</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last">The properties for managing dynamic environment maps on this tileset.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>backFaceCulling</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    true
                
                </td>
            

            <td class="description last">Whether to cull back-facing geometry. When true, back face culling is determined by the glTF material's doubleSided property; when false, back face culling is disabled.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>enableShowOutline</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    true
                
                </td>
            

            <td class="description last">Whether to enable outlines for models using the <a href="https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/CESIUM_primitive_outline">CESIUM_primitive_outline</a> extension. This can be set to false to avoid the additional processing of geometry at load time. When false, the showOutlines and outlineColor options are ignored.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>showOutline</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    true
                
                </td>
            

            <td class="description last">Whether to display the outline for models using the <a href="https://github.com/KhronosGroup/glTF/tree/master/extensions/2.0/Vendor/CESIUM_primitive_outline">CESIUM_primitive_outline</a> extension. When true, outlines are displayed. When false, outlines are not displayed.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>outlineColor</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="Color.html">Color</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    Color.BLACK
                
                </td>
            

            <td class="description last">The color to use when rendering outlines.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>vectorClassificationOnly</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">Indicates that only the tileset's vector tiles should be used for classification.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>vectorKeepDecodedPositions</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">Whether vector tiles should keep decoded positions in memory. This is used with <code>Cesium3DTileFeature.getPolylinePositions</code>.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>featureIdLabel</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>
|

<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    "featureId_0"
                
                </td>
            

            <td class="description last">Label of the feature ID set to use for picking and styling. For EXT_mesh_features, this is the feature ID's label property, or "featureId_N" (where N is the index in the featureIds array) when not specified. EXT_feature_metadata did not have a label field, so such feature ID sets are always labeled "featureId_N" where N is the index in the list of all feature Ids, where feature ID attributes are listed before feature ID textures. If featureIdLabel is an integer N, it is converted to the string "featureId_N" automatically. If both per-primitive and per-instance feature IDs are present, the instance feature IDs take priority.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>instanceFeatureIdLabel</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>
|

<span class="param-type">number</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    "instanceFeatureId_0"
                
                </td>
            

            <td class="description last">Label of the instance feature ID set used for picking and styling. If instanceFeatureIdLabel is set to an integer N, it is converted to the string "instanceFeatureId_N" automatically. If both per-primitive and per-instance feature IDs are present, the instance feature IDs take priority.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>showCreditsOnScreen</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">Whether to display the credits of this tileset on screen.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>splitDirection</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#SplitDirection">SplitDirection</a></span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    SplitDirection.NONE
                
                </td>
            

            <td class="description last">The <a href="global.html#SplitDirection"><code>SplitDirection</code></a> split to apply to this tileset.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>enableCollision</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">When <code>true</code>, enables collisions for camera or CPU picking. While this is <code>true</code> the camera will be prevented from going below the tileset surface if <a href="ScreenSpaceCameraController.html#enableCollisionDetection"><code>ScreenSpaceCameraController#enableCollisionDetection</code></a> is true. This also affects the behavior of <a href="global.html#HeightReference#.CLAMP_TO_GROUND"><code>HeightReference.CLAMP_TO_GROUND</code></a> when clamping to 3D Tiles surfaces. If <code>enableCollision</code> is <code>false</code>, entities may not be correctly clamped to the tileset geometry.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>projectTo2D</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">Whether to accurately project the tileset to 2D. If this is true, the tileset will be projected accurately to 2D, but it will use more memory to do so. If this is false, the tileset will use less memory and will still render in 2D / CV mode, but its projected positions may be inaccurate. This cannot be set after the tileset has been created.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>enablePick</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">Whether to allow collision and CPU picking with <code>pick</code> when using WebGL 1. If using WebGL 2 or above, this option will be ignored. If using WebGL 1 and this is true, the <code>pick</code> operation will work correctly, but it will use more memory to do so. If running with WebGL 1 and this is false, the model will use less memory, but <code>pick</code> will always return <code>undefined</code>. This cannot be set after the tileset has loaded.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>asynchronouslyLoadImagery</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">Whether loading imagery that is draped over the tileset should be done asynchronously. If this is <code>true</code>, then tile content will be displayed with its original texture until the imagery texture is loaded. If this is <code>false</code>, then the tile content will not be displayed until the imagery is ready.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>debugHeatmapTilePropertyName</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last">The tile variable to colorize as a heatmap. All rendered tiles will be colorized relative to each other's specified variable value.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>debugFreezeFrame</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">For debugging only. Determines if only the tiles from last frame should be used for rendering.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>debugColorizeTiles</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">For debugging only. When true, assigns a random color to each tile.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>enableDebugWireframe</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">For debugging only. This must be true for debugWireframe to work in WebGL1. This cannot be set after the tileset has been created.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>debugWireframe</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">For debugging only. When true, render's each tile's content as a wireframe.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>debugShowBoundingVolume</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">For debugging only. When true, renders the bounding volume for each tile.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>debugShowContentBoundingVolume</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">For debugging only. When true, renders the bounding volume for each tile's content.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>debugShowViewerRequestVolume</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">For debugging only. When true, renders the viewer request volume for each tile.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>debugShowGeometricError</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">For debugging only. When true, draws labels to indicate the geometric error of each tile.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>debugShowRenderingStatistics</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">For debugging only. When true, draws labels to indicate the number of commands, points, triangles and features for each tile.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>debugShowMemoryUsage</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">For debugging only. When true, draws labels to indicate the texture and geometry memory in megabytes used by each tile.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>debugShowUrl</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            
                <td class="default">
                
                    false
                
                </td>
            

            <td class="description last">For debugging only. When true, draws labels to indicate the url of each tile.</td>
        </tr>

    
    </tbody>
</table>



    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


            
                
    <div class="nameContainer">
    <h4 class="name" id=".foveatedInterpolationCallback">
        <a href="#.foveatedInterpolationCallback" class="doc-link"></a>
        Cesium.Cesium3DTileset.foveatedInterpolationCallback<span class="signature">(p, q, time)</span> &rarr; <span class="type-signature returnType">number</span>
        

<div class="source-link rightLinks">
    <a href="https://github.com/CesiumGS/cesium/blob/1.132/packages/engine/Source/Scene/Cesium3DTileset.js#L3920">engine/Source/Scene/Cesium3DTileset.js 3920</a>
</div>


    </h4>

    </div>

    


<div class="description">
    Optimization option. Used as a callback when <a href="Cesium3DTileset.html#foveatedScreenSpaceError"><code>Cesium3DTileset#foveatedScreenSpaceError</code></a> is true to control how much to raise the screen space error for tiles outside the foveated cone,
interpolating between <a href="Cesium3DTileset.html#foveatedMinimumScreenSpaceErrorRelaxation"><code>Cesium3DTileset#foveatedMinimumScreenSpaceErrorRelaxation</code></a> and <a href="Cesium3DTileset.html#maximumScreenSpaceError"><code>Cesium3DTileset#maximumScreenSpaceError</code></a>.
</div>











    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>p</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                The start value to interpolate.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>q</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                The end value to interpolate.</td>
        </tr>

    

        <tr>
            
                <td class="name"><code>time</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>


            
            </td>

            

            <td class="description last">
            
                The time of interpolation generally in the range <code>[0.0, 1.0]</code>.</td>
        </tr>

    
    </tbody>
</table>











<h5>Returns:</h5>

        
<div class="param-desc">
    The interpolated value.
</div>


    




<dl class="details">


    

    

    

    

    

    

    

    
    <span class="details-header">Default Value:</span>
    <code class="language-javascript">Math.lerp</code>
    

    

    

    

    

    

    
</dl>


            
    

    
</article>

</section>





    <div class="help">
        Need help? The fastest way to get answers is from the community and team on the <a href="https://community.cesium.com/">Cesium Forum</a>.
    </div>

    <footer>
        Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a>
    </footer>
</div>

<div class="nav">
    <div class="menu">
        <div class="search-wrapper">
            <input type="text" class="classFilter" id="ClassFilter" placeholder="Search">
            <div class="shortcut"><kbd>Ctrl</kbd><kbd>K</kbd></div>
        </div>
        <div id="ClassList"><h5>packages/engine</h5><ul><li data-name="AnchorPointDirect"><a href="AnchorPointDirect.html">AnchorPointDirect</a></li><li data-name="AnchorPointIndirect"><a href="AnchorPointIndirect.html">AnchorPointIndirect</a></li><li data-name="Appearance"><a href="Appearance.html">Appearance</a></li><li data-name="ArcGisBaseMapType"><a href="global.html#ArcGisBaseMapType">ArcGisBaseMapType</a></li><li data-name="ArcGisMapServerImageryProvider"><a href="ArcGisMapServerImageryProvider.html">ArcGisMapServerImageryProvider</a></li><li data-name="ArcGisMapService"><a href="ArcGisMapService.html">ArcGisMapService</a></li><li data-name="ArcGISTiledElevationTerrainProvider"><a href="ArcGISTiledElevationTerrainProvider.html">ArcGISTiledElevationTerrainProvider</a></li><li data-name="ArcType"><a href="global.html#ArcType">ArcType</a></li><li data-name="AssociativeArray"><a href="AssociativeArray.html">AssociativeArray</a></li><li data-name="Atmosphere"><a href="Atmosphere.html">Atmosphere</a></li><li data-name="availableLevels"><a href="global.html#availableLevels">availableLevels</a></li><li data-name="Axis"><a href="global.html#Axis">Axis</a></li><li data-name="AxisAlignedBoundingBox"><a href="AxisAlignedBoundingBox.html">AxisAlignedBoundingBox</a></li><li data-name="barycentricCoordinates"><a href="global.html#barycentricCoordinates">barycentricCoordinates</a></li><li data-name="Billboard"><a href="Billboard.html">Billboard</a></li><li data-name="BillboardCollection"><a href="BillboardCollection.html">BillboardCollection</a></li><li data-name="BillboardGraphics"><a href="BillboardGraphics.html">BillboardGraphics</a></li><li data-name="BillboardVisualizer"><a href="BillboardVisualizer.html">BillboardVisualizer</a></li><li data-name="binarySearch"><a href="global.html#binarySearch">binarySearch</a></li><li data-name="binarySearchComparator"><a href="global.html#binarySearchComparator">binarySearchComparator</a></li><li data-name="BingMapsGeocoderService"><a href="BingMapsGeocoderService.html">BingMapsGeocoderService</a></li><li data-name="BingMapsImageryProvider"><a href="BingMapsImageryProvider.html">BingMapsImageryProvider</a></li><li data-name="BingMapsStyle"><a href="global.html#BingMapsStyle">BingMapsStyle</a></li><li data-name="BlendEquation"><a href="global.html#BlendEquation">BlendEquation</a></li><li data-name="BlendFunction"><a href="global.html#BlendFunction">BlendFunction</a></li><li data-name="BlendingState"><a href="BlendingState.html">BlendingState</a></li><li data-name="BlendOption"><a href="global.html#BlendOption">BlendOption</a></li><li data-name="BoundingRectangle"><a href="BoundingRectangle.html">BoundingRectangle</a></li><li data-name="BoundingSphere"><a href="BoundingSphere.html">BoundingSphere</a></li><li data-name="BoxEmitter"><a href="BoxEmitter.html">BoxEmitter</a></li><li data-name="BoxGeometry"><a href="BoxGeometry.html">BoxGeometry</a></li><li data-name="BoxGeometryUpdater"><a href="BoxGeometryUpdater.html">BoxGeometryUpdater</a></li><li data-name="BoxGraphics"><a href="BoxGraphics.html">BoxGraphics</a></li><li data-name="BoxOutlineGeometry"><a href="BoxOutlineGeometry.html">BoxOutlineGeometry</a></li><li data-name="buildModuleUrl"><a href="global.html#buildModuleUrl">buildModuleUrl</a></li><li data-name="CallbackPositionProperty"><a href="CallbackPositionProperty.html">CallbackPositionProperty</a></li><li data-name="CallbackProperty"><a href="CallbackProperty.html">CallbackProperty</a></li><li data-name="Camera"><a href="Camera.html">Camera</a></li><li data-name="CameraEventAggregator"><a href="CameraEventAggregator.html">CameraEventAggregator</a></li><li data-name="CameraEventType"><a href="global.html#CameraEventType">CameraEventType</a></li><li data-name="Cartesian2"><a href="Cartesian2.html">Cartesian2</a></li><li data-name="Cartesian3"><a href="Cartesian3.html">Cartesian3</a></li><li data-name="Cartesian4"><a href="Cartesian4.html">Cartesian4</a></li><li data-name="Cartographic"><a href="Cartographic.html">Cartographic</a></li><li data-name="CartographicGeocoderService"><a href="CartographicGeocoderService.html">CartographicGeocoderService</a></li><li data-name="CatmullRomSpline"><a href="CatmullRomSpline.html">CatmullRomSpline</a></li><li data-name="Cesium3DTile"><a href="Cesium3DTile.html">Cesium3DTile</a></li><li data-name="Cesium3DTileColorBlendMode"><a href="global.html#Cesium3DTileColorBlendMode">Cesium3DTileColorBlendMode</a></li><li data-name="Cesium3DTileContent"><a href="Cesium3DTileContent.html">Cesium3DTileContent</a></li><li data-name="Cesium3DTileFeature"><a href="Cesium3DTileFeature.html">Cesium3DTileFeature</a></li><li data-name="Cesium3DTilePointFeature"><a href="Cesium3DTilePointFeature.html">Cesium3DTilePointFeature</a></li><li data-name="Cesium3DTileset"><a href="Cesium3DTileset.html">Cesium3DTileset</a></li><li data-name="Cesium3DTilesetGraphics"><a href="Cesium3DTilesetGraphics.html">Cesium3DTilesetGraphics</a></li><li data-name="Cesium3DTilesetVisualizer"><a href="Cesium3DTilesetVisualizer.html">Cesium3DTilesetVisualizer</a></li><li data-name="Cesium3DTileStyle"><a href="Cesium3DTileStyle.html">Cesium3DTileStyle</a></li><li data-name="Cesium3DTilesVoxelProvider"><a href="Cesium3DTilesVoxelProvider.html">Cesium3DTilesVoxelProvider</a></li><li data-name="CesiumTerrainProvider"><a href="CesiumTerrainProvider.html">CesiumTerrainProvider</a></li><li data-name="CesiumWidget"><a href="CesiumWidget.html">CesiumWidget</a></li><li data-name="Check"><a href="global.html#Check">Check</a></li><li data-name="CheckerboardMaterialProperty"><a href="CheckerboardMaterialProperty.html">CheckerboardMaterialProperty</a></li><li data-name="CircleEmitter"><a href="CircleEmitter.html">CircleEmitter</a></li><li data-name="CircleGeometry"><a href="CircleGeometry.html">CircleGeometry</a></li><li data-name="CircleOutlineGeometry"><a href="CircleOutlineGeometry.html">CircleOutlineGeometry</a></li><li data-name="ClassificationPrimitive"><a href="ClassificationPrimitive.html">ClassificationPrimitive</a></li><li data-name="ClassificationType"><a href="global.html#ClassificationType">ClassificationType</a></li><li data-name="className"><a href="global.html#className">className</a></li><li data-name="classProperty"><a href="global.html#classProperty">classProperty</a></li><li data-name="ClippingPlane"><a href="ClippingPlane.html">ClippingPlane</a></li><li data-name="ClippingPlaneCollection"><a href="ClippingPlaneCollection.html">ClippingPlaneCollection</a></li><li data-name="ClippingPolygon"><a href="ClippingPolygon.html">ClippingPolygon</a></li><li data-name="ClippingPolygonCollection"><a href="ClippingPolygonCollection.html">ClippingPolygonCollection</a></li><li data-name="Clock"><a href="Clock.html">Clock</a></li><li data-name="ClockRange"><a href="global.html#ClockRange">ClockRange</a></li><li data-name="ClockStep"><a href="global.html#ClockStep">ClockStep</a></li><li data-name="clone"><a href="global.html#clone">clone</a></li><li data-name="CloudCollection"><a href="CloudCollection.html">CloudCollection</a></li><li data-name="CloudType"><a href="global.html#CloudType">CloudType</a></li><li data-name="Color"><a href="Color.html">Color</a></li><li data-name="ColorBlendMode"><a href="global.html#ColorBlendMode">ColorBlendMode</a></li><li data-name="ColorGeometryInstanceAttribute"><a href="ColorGeometryInstanceAttribute.html">ColorGeometryInstanceAttribute</a></li><li data-name="ColorMaterialProperty"><a href="ColorMaterialProperty.html">ColorMaterialProperty</a></li><li data-name="combine"><a href="global.html#combine">combine</a></li><li data-name="ComponentDatatype"><a href="global.html#ComponentDatatype">ComponentDatatype</a></li><li data-name="ComponentReaderCallback"><a href="global.html#ComponentReaderCallback">ComponentReaderCallback</a></li><li data-name="ComponentsReaderCallback"><a href="global.html#ComponentsReaderCallback">ComponentsReaderCallback</a></li><li data-name="CompositeEntityCollection"><a href="CompositeEntityCollection.html">CompositeEntityCollection</a></li><li data-name="CompositeMaterialProperty"><a href="CompositeMaterialProperty.html">CompositeMaterialProperty</a></li><li data-name="CompositePositionProperty"><a href="CompositePositionProperty.html">CompositePositionProperty</a></li><li data-name="CompositeProperty"><a href="CompositeProperty.html">CompositeProperty</a></li><li data-name="CompressedTextureBuffer"><a href="CompressedTextureBuffer.html">CompressedTextureBuffer</a></li><li data-name="computePickingDrawingBufferRectangle"><a href="global.html#computePickingDrawingBufferRectangle">computePickingDrawingBufferRectangle</a></li><li data-name="ConditionsExpression"><a href="ConditionsExpression.html">ConditionsExpression</a></li><li data-name="ConeEmitter"><a href="ConeEmitter.html">ConeEmitter</a></li><li data-name="ConstantPositionProperty"><a href="ConstantPositionProperty.html">ConstantPositionProperty</a></li><li data-name="ConstantProperty"><a href="ConstantProperty.html">ConstantProperty</a></li><li data-name="ConstantSpline"><a href="ConstantSpline.html">ConstantSpline</a></li><li data-name="ContextOptions"><a href="global.html#ContextOptions">ContextOptions</a></li><li data-name="CoplanarPolygonGeometry"><a href="CoplanarPolygonGeometry.html">CoplanarPolygonGeometry</a></li><li data-name="CoplanarPolygonOutlineGeometry"><a href="CoplanarPolygonOutlineGeometry.html">CoplanarPolygonOutlineGeometry</a></li><li data-name="CornerType"><a href="global.html#CornerType">CornerType</a></li><li data-name="CorrelationGroup"><a href="CorrelationGroup.html">CorrelationGroup</a></li><li data-name="CorridorGeometry"><a href="CorridorGeometry.html">CorridorGeometry</a></li><li data-name="CorridorGeometryUpdater"><a href="CorridorGeometryUpdater.html">CorridorGeometryUpdater</a></li><li data-name="CorridorGraphics"><a href="CorridorGraphics.html">CorridorGraphics</a></li><li data-name="CorridorOutlineGeometry"><a href="CorridorOutlineGeometry.html">CorridorOutlineGeometry</a></li><li data-name="createAnchorPointDirect"><a href="global.html#createAnchorPointDirect">createAnchorPointDirect</a></li><li data-name="createAnchorPointIndirect"><a href="global.html#createAnchorPointIndirect">createAnchorPointIndirect</a></li><li data-name="createCorrelationGroup"><a href="global.html#createCorrelationGroup">createCorrelationGroup</a></li><li data-name="createCovarianceMatrixFromUpperTriangle"><a href="global.html#createCovarianceMatrixFromUpperTriangle">createCovarianceMatrixFromUpperTriangle</a></li><li data-name="createElevationBandMaterial"><a href="global.html#createElevationBandMaterial">createElevationBandMaterial</a></li><li data-name="createElevationBandMaterialBand"><a href="global.html#createElevationBandMaterialBand">createElevationBandMaterialBand</a></li><li data-name="createElevationBandMaterialEntry"><a href="global.html#createElevationBandMaterialEntry">createElevationBandMaterialEntry</a></li><li data-name="createGooglePhotorealistic3DTileset"><a href="global.html#createGooglePhotorealistic3DTileset">createGooglePhotorealistic3DTileset</a></li><li data-name="createGuid"><a href="global.html#createGuid">createGuid</a></li><li data-name="createOsmBuildingsAsync"><a href="global.html#createOsmBuildingsAsync">createOsmBuildingsAsync</a></li><li data-name="createTangentSpaceDebugPrimitive"><a href="global.html#createTangentSpaceDebugPrimitive">createTangentSpaceDebugPrimitive</a></li><li data-name="createWorldBathymetryAsync"><a href="global.html#createWorldBathymetryAsync">createWorldBathymetryAsync</a></li><li data-name="createWorldImageryAsync"><a href="global.html#createWorldImageryAsync">createWorldImageryAsync</a></li><li data-name="createWorldTerrainAsync"><a href="global.html#createWorldTerrainAsync">createWorldTerrainAsync</a></li><li data-name="Credit"><a href="Credit.html">Credit</a></li><li data-name="CreditDisplay"><a href="CreditDisplay.html">CreditDisplay</a></li><li data-name="CubicRealPolynomial"><a href="CubicRealPolynomial.html">CubicRealPolynomial</a></li><li data-name="CullFace"><a href="global.html#CullFace">CullFace</a></li><li data-name="CullingVolume"><a href="CullingVolume.html">CullingVolume</a></li><li data-name="CumulusCloud"><a href="CumulusCloud.html">CumulusCloud</a></li><li data-name="CustomDataSource"><a href="CustomDataSource.html">CustomDataSource</a></li><li data-name="CustomHeightmapTerrainProvider"><a href="CustomHeightmapTerrainProvider.html">CustomHeightmapTerrainProvider</a></li><li data-name="CustomShader"><a href="CustomShader.html">CustomShader</a></li><li data-name="CustomShaderMode"><a href="global.html#CustomShaderMode">CustomShaderMode</a></li><li data-name="CustomShaderTranslucencyMode"><a href="global.html#CustomShaderTranslucencyMode">CustomShaderTranslucencyMode</a></li><li data-name="CylinderGeometry"><a href="CylinderGeometry.html">CylinderGeometry</a></li><li data-name="CylinderGeometryUpdater"><a href="CylinderGeometryUpdater.html">CylinderGeometryUpdater</a></li><li data-name="CylinderGraphics"><a href="CylinderGraphics.html">CylinderGraphics</a></li><li data-name="CylinderOutlineGeometry"><a href="CylinderOutlineGeometry.html">CylinderOutlineGeometry</a></li><li data-name="CzmlDataSource"><a href="CzmlDataSource.html">CzmlDataSource</a></li><li data-name="DataSource"><a href="DataSource.html">DataSource</a></li><li data-name="DataSourceClock"><a href="DataSourceClock.html">DataSourceClock</a></li><li data-name="DataSourceCollection"><a href="DataSourceCollection.html">DataSourceCollection</a></li><li data-name="DataSourceDisplay"><a href="DataSourceDisplay.html">DataSourceDisplay</a></li><li data-name="DebugAppearance"><a href="DebugAppearance.html">DebugAppearance</a></li><li data-name="DebugCameraPrimitive"><a href="DebugCameraPrimitive.html">DebugCameraPrimitive</a></li><li data-name="DebugModelMatrixPrimitive"><a href="DebugModelMatrixPrimitive.html">DebugModelMatrixPrimitive</a></li><li data-name="DefaultProxy"><a href="DefaultProxy.html">DefaultProxy</a></li><li data-name="defaultValue"><a href="global.html#defaultValue">defaultValue</a></li><li data-name="defined"><a href="global.html#defined">defined</a></li><li data-name="DepthFunction"><a href="global.html#DepthFunction">DepthFunction</a></li><li data-name="destroyObject"><a href="global.html#destroyObject">destroyObject</a></li><li data-name="DeveloperError"><a href="DeveloperError.html">DeveloperError</a></li><li data-name="DirectionalLight"><a href="DirectionalLight.html">DirectionalLight</a></li><li data-name="DirectionUp"><a href="global.html#DirectionUp">DirectionUp</a></li><li data-name="DiscardEmptyTileImagePolicy"><a href="DiscardEmptyTileImagePolicy.html">DiscardEmptyTileImagePolicy</a></li><li data-name="DiscardMissingTileImagePolicy"><a href="DiscardMissingTileImagePolicy.html">DiscardMissingTileImagePolicy</a></li><li data-name="DistanceDisplayCondition"><a href="DistanceDisplayCondition.html">DistanceDisplayCondition</a></li><li data-name="DistanceDisplayConditionGeometryInstanceAttribute"><a href="DistanceDisplayConditionGeometryInstanceAttribute.html">DistanceDisplayConditionGeometryInstanceAttribute</a></li><li data-name="DONE"><a href="global.html#DONE">DONE</a></li><li data-name="DynamicAtmosphereLightingType"><a href="global.html#DynamicAtmosphereLightingType">DynamicAtmosphereLightingType</a></li><li data-name="DynamicEnvironmentMapManager"><a href="DynamicEnvironmentMapManager.html">DynamicEnvironmentMapManager</a></li><li data-name="EasingFunction"><a href="EasingFunction.html">EasingFunction</a></li><li data-name="EllipseGeometry"><a href="EllipseGeometry.html">EllipseGeometry</a></li><li data-name="EllipseGeometryUpdater"><a href="EllipseGeometryUpdater.html">EllipseGeometryUpdater</a></li><li data-name="EllipseGraphics"><a href="EllipseGraphics.html">EllipseGraphics</a></li><li data-name="EllipseOutlineGeometry"><a href="EllipseOutlineGeometry.html">EllipseOutlineGeometry</a></li><li data-name="Ellipsoid"><a href="Ellipsoid.html">Ellipsoid</a></li><li data-name="EllipsoidGeodesic"><a href="EllipsoidGeodesic.html">EllipsoidGeodesic</a></li><li data-name="EllipsoidGeometry"><a href="EllipsoidGeometry.html">EllipsoidGeometry</a></li><li data-name="EllipsoidGeometryUpdater"><a href="EllipsoidGeometryUpdater.html">EllipsoidGeometryUpdater</a></li><li data-name="EllipsoidGraphics"><a href="EllipsoidGraphics.html">EllipsoidGraphics</a></li><li data-name="EllipsoidOutlineGeometry"><a href="EllipsoidOutlineGeometry.html">EllipsoidOutlineGeometry</a></li><li data-name="EllipsoidRhumbLine"><a href="EllipsoidRhumbLine.html">EllipsoidRhumbLine</a></li><li data-name="EllipsoidSurfaceAppearance"><a href="EllipsoidSurfaceAppearance.html">EllipsoidSurfaceAppearance</a></li><li data-name="EllipsoidTangentPlane"><a href="EllipsoidTangentPlane.html">EllipsoidTangentPlane</a></li><li data-name="EllipsoidTerrainProvider"><a href="EllipsoidTerrainProvider.html">EllipsoidTerrainProvider</a></li><li data-name="Entity"><a href="Entity.html">Entity</a></li><li data-name="EntityCluster"><a href="EntityCluster.html">EntityCluster</a></li><li data-name="EntityCollection"><a href="EntityCollection.html">EntityCollection</a></li><li data-name="EntityView"><a href="EntityView.html">EntityView</a></li><li data-name="Event"><a href="Event.html">Event</a></li><li data-name="EventHelper"><a href="EventHelper.html">EventHelper</a></li><li data-name="excludesReverseAxis"><a href="global.html#excludesReverseAxis">excludesReverseAxis</a></li><li data-name="exportKml"><a href="global.html#exportKml">exportKml</a></li><li data-name="exportKmlModelCallback"><a href="global.html#exportKmlModelCallback">exportKmlModelCallback</a></li><li data-name="exportKmlResultKml"><a href="global.html#exportKmlResultKml">exportKmlResultKml</a></li><li data-name="exportKmlResultKmz"><a href="global.html#exportKmlResultKmz">exportKmlResultKmz</a></li><li data-name="Expression"><a href="Expression.html">Expression</a></li><li data-name="ExtrapolationType"><a href="global.html#ExtrapolationType">ExtrapolationType</a></li><li data-name="FAILED"><a href="global.html#FAILED">FAILED</a></li><li data-name="FeatureDetection"><a href="FeatureDetection.html">FeatureDetection</a></li><li data-name="Fog"><a href="Fog.html">Fog</a></li><li data-name="formatError"><a href="global.html#formatError">formatError</a></li><li data-name="FrameRateMonitor"><a href="FrameRateMonitor.html">FrameRateMonitor</a></li><li data-name="Frozen"><a href="Frozen.html">Frozen</a></li><li data-name="FrustumGeometry"><a href="FrustumGeometry.html">FrustumGeometry</a></li><li data-name="FrustumOutlineGeometry"><a href="FrustumOutlineGeometry.html">FrustumOutlineGeometry</a></li><li data-name="Fullscreen"><a href="Fullscreen.html">Fullscreen</a></li><li data-name="GeocoderService"><a href="GeocoderService.html">GeocoderService</a></li><li data-name="GeocodeType"><a href="global.html#GeocodeType">GeocodeType</a></li><li data-name="GeographicProjection"><a href="GeographicProjection.html">GeographicProjection</a></li><li data-name="GeographicTilingScheme"><a href="GeographicTilingScheme.html">GeographicTilingScheme</a></li><li data-name="GeoJsonDataSource"><a href="GeoJsonDataSource.html">GeoJsonDataSource</a></li><li data-name="Geometry"><a href="Geometry.html">Geometry</a></li><li data-name="GeometryAttribute"><a href="GeometryAttribute.html">GeometryAttribute</a></li><li data-name="GeometryAttributes"><a href="GeometryAttributes.html">GeometryAttributes</a></li><li data-name="GeometryFactory"><a href="GeometryFactory.html">GeometryFactory</a></li><li data-name="GeometryInstance"><a href="GeometryInstance.html">GeometryInstance</a></li><li data-name="GeometryInstanceAttribute"><a href="GeometryInstanceAttribute.html">GeometryInstanceAttribute</a></li><li data-name="GeometryPipeline"><a href="GeometryPipeline.html">GeometryPipeline</a></li><li data-name="GeometryUpdater"><a href="GeometryUpdater.html">GeometryUpdater</a></li><li data-name="geometryUpdaters"><a href="global.html#geometryUpdaters">geometryUpdaters</a></li><li data-name="GeometryVisualizer"><a href="GeometryVisualizer.html">GeometryVisualizer</a></li><li data-name="getAbsoluteUri"><a href="global.html#getAbsoluteUri">getAbsoluteUri</a></li><li data-name="getBaseUri"><a href="global.html#getBaseUri">getBaseUri</a></li><li data-name="getExtensionFromUri"><a href="global.html#getExtensionFromUri">getExtensionFromUri</a></li><li data-name="GetFeatureInfoFormat"><a href="GetFeatureInfoFormat.html">GetFeatureInfoFormat</a></li><li data-name="getFilenameFromUri"><a href="global.html#getFilenameFromUri">getFilenameFromUri</a></li><li data-name="getGlslType"><a href="global.html#getGlslType">getGlslType</a></li><li data-name="getImagePixels"><a href="global.html#getImagePixels">getImagePixels</a></li><li data-name="getSourceValueStringComponent"><a href="global.html#getSourceValueStringComponent">getSourceValueStringComponent</a></li><li data-name="getSourceValueStringScalar"><a href="global.html#getSourceValueStringScalar">getSourceValueStringScalar</a></li><li data-name="getTimestamp"><a href="global.html#getTimestamp">getTimestamp</a></li><li data-name="Globe"><a href="Globe.html">Globe</a></li><li data-name="GlobeTranslucency"><a href="GlobeTranslucency.html">GlobeTranslucency</a></li><li data-name="GltfGpmLocal"><a href="GltfGpmLocal.html">GltfGpmLocal</a></li><li data-name="GoogleEarthEnterpriseImageryProvider"><a href="GoogleEarthEnterpriseImageryProvider.html">GoogleEarthEnterpriseImageryProvider</a></li><li data-name="GoogleEarthEnterpriseMapsProvider"><a href="GoogleEarthEnterpriseMapsProvider.html">GoogleEarthEnterpriseMapsProvider</a></li><li data-name="GoogleEarthEnterpriseMetadata"><a href="GoogleEarthEnterpriseMetadata.html">GoogleEarthEnterpriseMetadata</a></li><li data-name="GoogleEarthEnterpriseTerrainData"><a href="GoogleEarthEnterpriseTerrainData.html">GoogleEarthEnterpriseTerrainData</a></li><li data-name="GoogleEarthEnterpriseTerrainProvider"><a href="GoogleEarthEnterpriseTerrainProvider.html">GoogleEarthEnterpriseTerrainProvider</a></li><li data-name="GoogleGeocoderService"><a href="GoogleGeocoderService.html">GoogleGeocoderService</a></li><li data-name="GoogleMaps"><a href="GoogleMaps.html">GoogleMaps</a></li><li data-name="GpxDataSource"><a href="GpxDataSource.html">GpxDataSource</a></li><li data-name="GregorianDate"><a href="GregorianDate.html">GregorianDate</a></li><li data-name="GridImageryProvider"><a href="GridImageryProvider.html">GridImageryProvider</a></li><li data-name="GridMaterialProperty"><a href="GridMaterialProperty.html">GridMaterialProperty</a></li><li data-name="GroundGeometryUpdater"><a href="GroundGeometryUpdater.html">GroundGeometryUpdater</a></li><li data-name="GroundPolylineGeometry"><a href="GroundPolylineGeometry.html">GroundPolylineGeometry</a></li><li data-name="GroundPolylinePrimitive"><a href="GroundPolylinePrimitive.html">GroundPolylinePrimitive</a></li><li data-name="GroundPrimitive"><a href="GroundPrimitive.html">GroundPrimitive</a></li><li data-name="HeadingPitchRange"><a href="HeadingPitchRange.html">HeadingPitchRange</a></li><li data-name="HeadingPitchRoll"><a href="HeadingPitchRoll.html">HeadingPitchRoll</a></li><li data-name="HeadingPitchRollValues"><a href="global.html#HeadingPitchRollValues">HeadingPitchRollValues</a></li><li data-name="HeightmapEncoding"><a href="global.html#HeightmapEncoding">HeightmapEncoding</a></li><li data-name="HeightmapTerrainData"><a href="HeightmapTerrainData.html">HeightmapTerrainData</a></li><li data-name="HeightReference"><a href="global.html#HeightReference">HeightReference</a></li><li data-name="HermitePolynomialApproximation"><a href="HermitePolynomialApproximation.html">HermitePolynomialApproximation</a></li><li data-name="HermiteSpline"><a href="HermiteSpline.html">HermiteSpline</a></li><li data-name="HilbertOrder"><a href="HilbertOrder.html">HilbertOrder</a></li><li data-name="HorizontalOrigin"><a href="global.html#HorizontalOrigin">HorizontalOrigin</a></li><li data-name="I3SDataProvider"><a href="I3SDataProvider.html">I3SDataProvider</a></li><li data-name="I3SFeature"><a href="I3SFeature.html">I3SFeature</a></li><li data-name="I3SField"><a href="I3SField.html">I3SField</a></li><li data-name="I3SGeometry"><a href="I3SGeometry.html">I3SGeometry</a></li><li data-name="I3SLayer"><a href="I3SLayer.html">I3SLayer</a></li><li data-name="I3SNode"><a href="I3SNode.html">I3SNode</a></li><li data-name="I3SStatistics"><a href="I3SStatistics.html">I3SStatistics</a></li><li data-name="I3SSublayer"><a href="I3SSublayer.html">I3SSublayer</a></li><li data-name="I3SSymbology"><a href="I3SSymbology.html">I3SSymbology</a></li><li data-name="ImageBasedLighting"><a href="ImageBasedLighting.html">ImageBasedLighting</a></li><li data-name="ImageMaterialProperty"><a href="ImageMaterialProperty.html">ImageMaterialProperty</a></li><li data-name="ImageryLayer"><a href="ImageryLayer.html">ImageryLayer</a></li><li data-name="ImageryLayerCollection"><a href="ImageryLayerCollection.html">ImageryLayerCollection</a></li><li data-name="ImageryLayerFeatureInfo"><a href="ImageryLayerFeatureInfo.html">ImageryLayerFeatureInfo</a></li><li data-name="ImageryProvider"><a href="ImageryProvider.html">ImageryProvider</a></li><li data-name="ImageryTypes"><a href="global.html#ImageryTypes">ImageryTypes</a></li><li data-name="includesReverseAxis"><a href="global.html#includesReverseAxis">includesReverseAxis</a></li><li data-name="IndexDatatype"><a href="global.html#IndexDatatype">IndexDatatype</a></li><li data-name="Intersect"><a href="global.html#Intersect">Intersect</a></li><li data-name="Intersections2D"><a href="Intersections2D.html">Intersections2D</a></li><li data-name="IntersectionTests"><a href="IntersectionTests.html">IntersectionTests</a></li><li data-name="Interval"><a href="Interval.html">Interval</a></li><li data-name="Ion"><a href="Ion.html">Ion</a></li><li data-name="IonGeocodeProviderType"><a href="global.html#IonGeocodeProviderType">IonGeocodeProviderType</a></li><li data-name="IonGeocoderService"><a href="IonGeocoderService.html">IonGeocoderService</a></li><li data-name="IonImageryProvider"><a href="IonImageryProvider.html">IonImageryProvider</a></li><li data-name="IonResource"><a href="IonResource.html">IonResource</a></li><li data-name="IonWorldImageryStyle"><a href="global.html#IonWorldImageryStyle">IonWorldImageryStyle</a></li><li data-name="isLeapYear"><a href="global.html#isLeapYear">isLeapYear</a></li><li data-name="Iso8601"><a href="Iso8601.html">Iso8601</a></li><li data-name="ITwinData"><a href="ITwinData.html">ITwinData</a></li><li data-name="ITwinPlatform"><a href="ITwinPlatform.html">ITwinPlatform</a></li><li data-name="JulianDate"><a href="JulianDate.html">JulianDate</a></li><li data-name="KeyboardEventModifier"><a href="global.html#KeyboardEventModifier">KeyboardEventModifier</a></li><li data-name="KmlCamera"><a href="KmlCamera.html">KmlCamera</a></li><li data-name="KmlDataSource"><a href="KmlDataSource.html">KmlDataSource</a></li><li data-name="KmlFeatureData"><a href="KmlFeatureData.html">KmlFeatureData</a></li><li data-name="KmlLookAt"><a href="KmlLookAt.html">KmlLookAt</a></li><li data-name="KmlTour"><a href="KmlTour.html">KmlTour</a></li><li data-name="KmlTourFlyTo"><a href="KmlTourFlyTo.html">KmlTourFlyTo</a></li><li data-name="KmlTourWait"><a href="KmlTourWait.html">KmlTourWait</a></li><li data-name="Label"><a href="Label.html">Label</a></li><li data-name="LabelCollection"><a href="LabelCollection.html">LabelCollection</a></li><li data-name="LabelGraphics"><a href="LabelGraphics.html">LabelGraphics</a></li><li data-name="LabelStyle"><a href="global.html#LabelStyle">LabelStyle</a></li><li data-name="LabelVisualizer"><a href="LabelVisualizer.html">LabelVisualizer</a></li><li data-name="LagrangePolynomialApproximation"><a href="LagrangePolynomialApproximation.html">LagrangePolynomialApproximation</a></li><li data-name="LeapSecond"><a href="LeapSecond.html">LeapSecond</a></li><li data-name="Light"><a href="Light.html">Light</a></li><li data-name="LightingModel"><a href="global.html#LightingModel">LightingModel</a></li><li data-name="LinearApproximation"><a href="LinearApproximation.html">LinearApproximation</a></li><li data-name="LinearSpline"><a href="LinearSpline.html">LinearSpline</a></li><li data-name="loadGltfJson"><a href="global.html#loadGltfJson">loadGltfJson</a></li><li data-name="LRUCache"><a href="LRUCache.html">LRUCache</a></li><li data-name="MapboxImageryProvider"><a href="MapboxImageryProvider.html">MapboxImageryProvider</a></li><li data-name="MapboxStyleImageryProvider"><a href="MapboxStyleImageryProvider.html">MapboxStyleImageryProvider</a></li><li data-name="MapMode2D"><a href="global.html#MapMode2D">MapMode2D</a></li><li data-name="MapProjection"><a href="MapProjection.html">MapProjection</a></li><li data-name="Material"><a href="Material.html">Material</a></li><li data-name="MaterialAppearance"><a href="MaterialAppearance.html">MaterialAppearance</a></li><li data-name="MaterialSupport"><a href="MaterialAppearance.MaterialSupport.html">MaterialSupport</a></li><li data-name="MaterialProperty"><a href="MaterialProperty.html">MaterialProperty</a></li><li data-name="Math"><a href="Math.html">Math</a></li><li data-name="Matrix2"><a href="Matrix2.html">Matrix2</a></li><li data-name="Matrix3"><a href="Matrix3.html">Matrix3</a></li><li data-name="Matrix4"><a href="Matrix4.html">Matrix4</a></li><li data-name="mergeSort"><a href="global.html#mergeSort">mergeSort</a></li><li data-name="mergeSortComparator"><a href="global.html#mergeSortComparator">mergeSortComparator</a></li><li data-name="metadata"><a href="global.html#metadata">metadata</a></li><li data-name="MetadataClass"><a href="MetadataClass.html">MetadataClass</a></li><li data-name="MetadataClassProperty"><a href="MetadataClassProperty.html">MetadataClassProperty</a></li><li data-name="MetadataComponentType"><a href="global.html#MetadataComponentType">MetadataComponentType</a></li><li data-name="MetadataEnum"><a href="MetadataEnum.html">MetadataEnum</a></li><li data-name="MetadataEnumValue"><a href="MetadataEnumValue.html">MetadataEnumValue</a></li><li data-name="metadataProperty"><a href="global.html#metadataProperty">metadataProperty</a></li><li data-name="MetadataSchema"><a href="MetadataSchema.html">MetadataSchema</a></li><li data-name="MetadataType"><a href="global.html#MetadataType">MetadataType</a></li><li data-name="MetadataValue"><a href="global.html#MetadataValue">MetadataValue</a></li><li data-name="Model"><a href="Model.html">Model</a></li><li data-name="ModelAnimation"><a href="ModelAnimation.html">ModelAnimation</a></li><li data-name="ModelAnimationCollection"><a href="ModelAnimationCollection.html">ModelAnimationCollection</a></li><li data-name="ModelAnimationLoop"><a href="global.html#ModelAnimationLoop">ModelAnimationLoop</a></li><li data-name="ModelFeature"><a href="ModelFeature.html">ModelFeature</a></li><li data-name="ModelGraphics"><a href="ModelGraphics.html">ModelGraphics</a></li><li data-name="ModelNode"><a href="ModelNode.html">ModelNode</a></li><li data-name="ModelVisualizer"><a href="ModelVisualizer.html">ModelVisualizer</a></li><li data-name="Moon"><a href="Moon.html">Moon</a></li><li data-name="MorphWeightSpline"><a href="MorphWeightSpline.html">MorphWeightSpline</a></li><li data-name="NearFarScalar"><a href="NearFarScalar.html">NearFarScalar</a></li><li data-name="NeverTileDiscardPolicy"><a href="NeverTileDiscardPolicy.html">NeverTileDiscardPolicy</a></li><li data-name="NodeTransformationProperty"><a href="NodeTransformationProperty.html">NodeTransformationProperty</a></li><li data-name="objectToQuery"><a href="global.html#objectToQuery">objectToQuery</a></li><li data-name="obtainTranslucentCommandExecutionFunction"><a href="global.html#obtainTranslucentCommandExecutionFunction">obtainTranslucentCommandExecutionFunction</a></li><li data-name="Occluder"><a href="Occluder.html">Occluder</a></li><li data-name="of"><a href="global.html#of">of</a></li><li data-name="OpenCageGeocoderService"><a href="OpenCageGeocoderService.html">OpenCageGeocoderService</a></li><li data-name="OpenStreetMapImageryProvider"><a href="OpenStreetMapImageryProvider.html">OpenStreetMapImageryProvider</a></li><li data-name="OrientedBoundingBox"><a href="OrientedBoundingBox.html">OrientedBoundingBox</a></li><li data-name="OrthographicFrustum"><a href="OrthographicFrustum.html">OrthographicFrustum</a></li><li data-name="OrthographicOffCenterFrustum"><a href="OrthographicOffCenterFrustum.html">OrthographicOffCenterFrustum</a></li><li data-name="PackableForInterpolation"><a href="PackableForInterpolation.html">PackableForInterpolation</a></li><li data-name="Particle"><a href="Particle.html">Particle</a></li><li data-name="ParticleBurst"><a href="ParticleBurst.html">ParticleBurst</a></li><li data-name="ParticleEmitter"><a href="ParticleEmitter.html">ParticleEmitter</a></li><li data-name="ParticleSystem"><a href="ParticleSystem.html">ParticleSystem</a></li><li data-name="PathGraphics"><a href="PathGraphics.html">PathGraphics</a></li><li data-name="PathVisualizer"><a href="PathVisualizer.html">PathVisualizer</a></li><li data-name="PeliasGeocoderService"><a href="PeliasGeocoderService.html">PeliasGeocoderService</a></li><li data-name="PENDING"><a href="global.html#PENDING">PENDING</a></li><li data-name="PerInstanceColorAppearance"><a href="PerInstanceColorAppearance.html">PerInstanceColorAppearance</a></li><li data-name="PerspectiveFrustum"><a href="PerspectiveFrustum.html">PerspectiveFrustum</a></li><li data-name="PerspectiveOffCenterFrustum"><a href="PerspectiveOffCenterFrustum.html">PerspectiveOffCenterFrustum</a></li><li data-name="PickedMetadataInfo"><a href="global.html#PickedMetadataInfo">PickedMetadataInfo</a></li><li data-name="PinBuilder"><a href="PinBuilder.html">PinBuilder</a></li><li data-name="PixelDatatype"><a href="global.html#PixelDatatype">PixelDatatype</a></li><li data-name="PixelFormat"><a href="global.html#PixelFormat">PixelFormat</a></li><li data-name="Plane"><a href="Plane.html">Plane</a></li><li data-name="PlaneGeometry"><a href="PlaneGeometry.html">PlaneGeometry</a></li><li data-name="PlaneGeometryUpdater"><a href="PlaneGeometryUpdater.html">PlaneGeometryUpdater</a></li><li data-name="PlaneGraphics"><a href="PlaneGraphics.html">PlaneGraphics</a></li><li data-name="PlaneOutlineGeometry"><a href="PlaneOutlineGeometry.html">PlaneOutlineGeometry</a></li><li data-name="PointCloudShading"><a href="PointCloudShading.html">PointCloudShading</a></li><li data-name="PointGraphics"><a href="PointGraphics.html">PointGraphics</a></li><li data-name="pointInsideTriangle"><a href="global.html#pointInsideTriangle">pointInsideTriangle</a></li><li data-name="PointPrimitive"><a href="PointPrimitive.html">PointPrimitive</a></li><li data-name="PointPrimitiveCollection"><a href="PointPrimitiveCollection.html">PointPrimitiveCollection</a></li><li data-name="PointVisualizer"><a href="PointVisualizer.html">PointVisualizer</a></li><li data-name="PolygonGeometry"><a href="PolygonGeometry.html">PolygonGeometry</a></li><li data-name="PolygonGeometryUpdater"><a href="PolygonGeometryUpdater.html">PolygonGeometryUpdater</a></li><li data-name="PolygonGraphics"><a href="PolygonGraphics.html">PolygonGraphics</a></li><li data-name="PolygonHierarchy"><a href="PolygonHierarchy.html">PolygonHierarchy</a></li><li data-name="PolygonOutlineGeometry"><a href="PolygonOutlineGeometry.html">PolygonOutlineGeometry</a></li><li data-name="Polyline"><a href="Polyline.html">Polyline</a></li><li data-name="PolylineArrowMaterialProperty"><a href="PolylineArrowMaterialProperty.html">PolylineArrowMaterialProperty</a></li><li data-name="PolylineCollection"><a href="PolylineCollection.html">PolylineCollection</a></li><li data-name="PolylineColorAppearance"><a href="PolylineColorAppearance.html">PolylineColorAppearance</a></li><li data-name="PolylineDashMaterialProperty"><a href="PolylineDashMaterialProperty.html">PolylineDashMaterialProperty</a></li><li data-name="PolylineGeometry"><a href="PolylineGeometry.html">PolylineGeometry</a></li><li data-name="PolylineGeometryUpdater"><a href="PolylineGeometryUpdater.html">PolylineGeometryUpdater</a></li><li data-name="PolylineGlowMaterialProperty"><a href="PolylineGlowMaterialProperty.html">PolylineGlowMaterialProperty</a></li><li data-name="PolylineGraphics"><a href="PolylineGraphics.html">PolylineGraphics</a></li><li data-name="PolylineMaterialAppearance"><a href="PolylineMaterialAppearance.html">PolylineMaterialAppearance</a></li><li data-name="PolylineOutlineMaterialProperty"><a href="PolylineOutlineMaterialProperty.html">PolylineOutlineMaterialProperty</a></li><li data-name="PolylineVisualizer"><a href="PolylineVisualizer.html">PolylineVisualizer</a></li><li data-name="PolylineVolumeGeometry"><a href="PolylineVolumeGeometry.html">PolylineVolumeGeometry</a></li><li data-name="PolylineVolumeGeometryUpdater"><a href="PolylineVolumeGeometryUpdater.html">PolylineVolumeGeometryUpdater</a></li><li data-name="PolylineVolumeGraphics"><a href="PolylineVolumeGraphics.html">PolylineVolumeGraphics</a></li><li data-name="PolylineVolumeOutlineGeometry"><a href="PolylineVolumeOutlineGeometry.html">PolylineVolumeOutlineGeometry</a></li><li data-name="PositionProperty"><a href="PositionProperty.html">PositionProperty</a></li><li data-name="PositionPropertyArray"><a href="PositionPropertyArray.html">PositionPropertyArray</a></li><li data-name="PostProcessStage"><a href="PostProcessStage.html">PostProcessStage</a></li><li data-name="PostProcessStageCollection"><a href="PostProcessStageCollection.html">PostProcessStageCollection</a></li><li data-name="PostProcessStageComposite"><a href="PostProcessStageComposite.html">PostProcessStageComposite</a></li><li data-name="PostProcessStageLibrary"><a href="PostProcessStageLibrary.html">PostProcessStageLibrary</a></li><li data-name="PostProcessStageSampleMode"><a href="global.html#PostProcessStageSampleMode">PostProcessStageSampleMode</a></li><li data-name="Primitive"><a href="Primitive.html">Primitive</a></li><li data-name="PrimitiveCollection"><a href="PrimitiveCollection.html">PrimitiveCollection</a></li><li data-name="PrimitiveType"><a href="global.html#PrimitiveType">PrimitiveType</a></li><li data-name="Property"><a href="Property.html">Property</a></li><li data-name="PropertyArray"><a href="PropertyArray.html">PropertyArray</a></li><li data-name="PropertyBag"><a href="PropertyBag.html">PropertyBag</a></li><li data-name="propertyName"><a href="global.html#propertyName">propertyName</a></li><li data-name="Proxy"><a href="Proxy.html">Proxy</a></li><li data-name="QuadraticRealPolynomial"><a href="QuadraticRealPolynomial.html">QuadraticRealPolynomial</a></li><li data-name="QuantizedMeshTerrainData"><a href="QuantizedMeshTerrainData.html">QuantizedMeshTerrainData</a></li><li data-name="QuarticRealPolynomial"><a href="QuarticRealPolynomial.html">QuarticRealPolynomial</a></li><li data-name="Quaternion"><a href="Quaternion.html">Quaternion</a></li><li data-name="QuaternionSpline"><a href="QuaternionSpline.html">QuaternionSpline</a></li><li data-name="queryToObject"><a href="global.html#queryToObject">queryToObject</a></li><li data-name="Queue"><a href="Queue.html">Queue</a></li><li data-name="Ray"><a href="Ray.html">Ray</a></li><li data-name="Rectangle"><a href="Rectangle.html">Rectangle</a></li><li data-name="RectangleGeometry"><a href="RectangleGeometry.html">RectangleGeometry</a></li><li data-name="RectangleGeometryUpdater"><a href="RectangleGeometryUpdater.html">RectangleGeometryUpdater</a></li><li data-name="RectangleGraphics"><a href="RectangleGraphics.html">RectangleGraphics</a></li><li data-name="RectangleOutlineGeometry"><a href="RectangleOutlineGeometry.html">RectangleOutlineGeometry</a></li><li data-name="ReferenceFrame"><a href="global.html#ReferenceFrame">ReferenceFrame</a></li><li data-name="ReferenceProperty"><a href="ReferenceProperty.html">ReferenceProperty</a></li><li data-name="removeExtension"><a href="global.html#removeExtension">removeExtension</a></li><li data-name="Request"><a href="Request.html">Request</a></li><li data-name="RequestErrorEvent"><a href="RequestErrorEvent.html">RequestErrorEvent</a></li><li data-name="RequestScheduler"><a href="RequestScheduler.html">RequestScheduler</a></li><li data-name="RequestState"><a href="global.html#RequestState">RequestState</a></li><li data-name="RequestType"><a href="global.html#RequestType">RequestType</a></li><li data-name="Resource"><a href="Resource.html">Resource</a></li><li data-name="RuntimeError"><a href="RuntimeError.html">RuntimeError</a></li><li data-name="SampledPositionProperty"><a href="SampledPositionProperty.html">SampledPositionProperty</a></li><li data-name="SampledProperty"><a href="SampledProperty.html">SampledProperty</a></li><li data-name="sampleTerrain"><a href="global.html#sampleTerrain">sampleTerrain</a></li><li data-name="sampleTerrainMostDetailed"><a href="global.html#sampleTerrainMostDetailed">sampleTerrainMostDetailed</a></li><li data-name="Scene"><a href="Scene.html">Scene</a></li><li data-name="SceneMode"><a href="global.html#SceneMode">SceneMode</a></li><li data-name="SceneTransforms"><a href="SceneTransforms.html">SceneTransforms</a></li><li data-name="schemaId"><a href="global.html#schemaId">schemaId</a></li><li data-name="ScreenSpaceCameraController"><a href="ScreenSpaceCameraController.html">ScreenSpaceCameraController</a></li><li data-name="ScreenSpaceEventHandler"><a href="ScreenSpaceEventHandler.html">ScreenSpaceEventHandler</a></li><li data-name="ScreenSpaceEventType"><a href="global.html#ScreenSpaceEventType">ScreenSpaceEventType</a></li><li data-name="SensorVolumePortionToDisplay"><a href="global.html#SensorVolumePortionToDisplay">SensorVolumePortionToDisplay</a></li><li data-name="ShadowMap"><a href="ShadowMap.html">ShadowMap</a></li><li data-name="ShadowMode"><a href="global.html#ShadowMode">ShadowMode</a></li><li data-name="ShowGeometryInstanceAttribute"><a href="ShowGeometryInstanceAttribute.html">ShowGeometryInstanceAttribute</a></li><li data-name="Simon1994PlanetaryPositions"><a href="Simon1994PlanetaryPositions.html">Simon1994PlanetaryPositions</a></li><li data-name="SimplePolylineGeometry"><a href="SimplePolylineGeometry.html">SimplePolylineGeometry</a></li><li data-name="SingleTileImageryProvider"><a href="SingleTileImageryProvider.html">SingleTileImageryProvider</a></li><li data-name="SkyAtmosphere"><a href="SkyAtmosphere.html">SkyAtmosphere</a></li><li data-name="SkyBox"><a href="SkyBox.html">SkyBox</a></li><li data-name="Spdcf"><a href="Spdcf.html">Spdcf</a></li><li data-name="SphereEmitter"><a href="SphereEmitter.html">SphereEmitter</a></li><li data-name="SphereGeometry"><a href="SphereGeometry.html">SphereGeometry</a></li><li data-name="SphereOutlineGeometry"><a href="SphereOutlineGeometry.html">SphereOutlineGeometry</a></li><li data-name="Spherical"><a href="Spherical.html">Spherical</a></li><li data-name="Spline"><a href="Spline.html">Spline</a></li><li data-name="SplitDirection"><a href="global.html#SplitDirection">SplitDirection</a></li><li data-name="srgbToLinear"><a href="global.html#srgbToLinear">srgbToLinear</a></li><li data-name="StencilFunction"><a href="global.html#StencilFunction">StencilFunction</a></li><li data-name="StencilOperation"><a href="global.html#StencilOperation">StencilOperation</a></li><li data-name="SteppedSpline"><a href="SteppedSpline.html">SteppedSpline</a></li><li data-name="Stereographic"><a href="global.html#Stereographic">Stereographic</a></li><li data-name="StorageType"><a href="global.html#StorageType">StorageType</a></li><li data-name="StripeMaterialProperty"><a href="StripeMaterialProperty.html">StripeMaterialProperty</a></li><li data-name="StripeOrientation"><a href="global.html#StripeOrientation">StripeOrientation</a></li><li data-name="StyleExpression"><a href="StyleExpression.html">StyleExpression</a></li><li data-name="subdivideArray"><a href="global.html#subdivideArray">subdivideArray</a></li><li data-name="Sun"><a href="Sun.html">Sun</a></li><li data-name="SunLight"><a href="SunLight.html">SunLight</a></li><li data-name="TaskProcessor"><a href="TaskProcessor.html">TaskProcessor</a></li><li data-name="Terrain"><a href="Terrain.html">Terrain</a></li><li data-name="TerrainData"><a href="TerrainData.html">TerrainData</a></li><li data-name="TerrainProvider"><a href="TerrainProvider.html">TerrainProvider</a></li><li data-name="TextureMagnificationFilter"><a href="global.html#TextureMagnificationFilter">TextureMagnificationFilter</a></li><li data-name="TextureMinificationFilter"><a href="global.html#TextureMinificationFilter">TextureMinificationFilter</a></li><li data-name="TextureUniform"><a href="TextureUniform.html">TextureUniform</a></li><li data-name="TILE_SIZE"><a href="global.html#TILE_SIZE">TILE_SIZE</a></li><li data-name="TileAvailability"><a href="TileAvailability.html">TileAvailability</a></li><li data-name="TileCoordinatesImageryProvider"><a href="TileCoordinatesImageryProvider.html">TileCoordinatesImageryProvider</a></li><li data-name="TileDiscardPolicy"><a href="TileDiscardPolicy.html">TileDiscardPolicy</a></li><li data-name="TileMapServiceImageryProvider"><a href="TileMapServiceImageryProvider.html">TileMapServiceImageryProvider</a></li><li data-name="TileProviderError"><a href="TileProviderError.html">TileProviderError</a></li><li data-name="TilingScheme"><a href="TilingScheme.html">TilingScheme</a></li><li data-name="TimeDynamicImagery"><a href="TimeDynamicImagery.html">TimeDynamicImagery</a></li><li data-name="TimeDynamicPointCloud"><a href="TimeDynamicPointCloud.html">TimeDynamicPointCloud</a></li><li data-name="TimeInterval"><a href="TimeInterval.html">TimeInterval</a></li><li data-name="TimeIntervalCollection"><a href="TimeIntervalCollection.html">TimeIntervalCollection</a></li><li data-name="TimeIntervalCollectionPositionProperty"><a href="TimeIntervalCollectionPositionProperty.html">TimeIntervalCollectionPositionProperty</a></li><li data-name="TimeIntervalCollectionProperty"><a href="TimeIntervalCollectionProperty.html">TimeIntervalCollectionProperty</a></li><li data-name="TimeStandard"><a href="global.html#TimeStandard">TimeStandard</a></li><li data-name="Tonemapper"><a href="global.html#Tonemapper">Tonemapper</a></li><li data-name="TrackingReferenceFrame"><a href="global.html#TrackingReferenceFrame">TrackingReferenceFrame</a></li><li data-name="Transforms"><a href="Transforms.html">Transforms</a></li><li data-name="TranslationRotationScale"><a href="TranslationRotationScale.html">TranslationRotationScale</a></li><li data-name="TridiagonalSystemSolver"><a href="TridiagonalSystemSolver.html">TridiagonalSystemSolver</a></li><li data-name="TrustedServers"><a href="TrustedServers.html">TrustedServers</a></li><li data-name="unapplyValueTransform"><a href="global.html#unapplyValueTransform">unapplyValueTransform</a></li><li data-name="UniformSpecifier"><a href="global.html#UniformSpecifier">UniformSpecifier</a></li><li data-name="UniformType"><a href="global.html#UniformType">UniformType</a></li><li data-name="unnormalize"><a href="global.html#unnormalize">unnormalize</a></li><li data-name="UrlTemplateImageryProvider"><a href="UrlTemplateImageryProvider.html">UrlTemplateImageryProvider</a></li><li data-name="VaryingType"><a href="global.html#VaryingType">VaryingType</a></li><li data-name="VelocityOrientationProperty"><a href="VelocityOrientationProperty.html">VelocityOrientationProperty</a></li><li data-name="VelocityVectorProperty"><a href="VelocityVectorProperty.html">VelocityVectorProperty</a></li><li data-name="VertexFormat"><a href="VertexFormat.html">VertexFormat</a></li><li data-name="VerticalOrigin"><a href="global.html#VerticalOrigin">VerticalOrigin</a></li><li data-name="VideoSynchronizer"><a href="VideoSynchronizer.html">VideoSynchronizer</a></li><li data-name="ViewportQuad"><a href="ViewportQuad.html">ViewportQuad</a></li><li data-name="Visibility"><a href="global.html#Visibility">Visibility</a></li><li data-name="Visualizer"><a href="Visualizer.html">Visualizer</a></li><li data-name="VoxelCell"><a href="VoxelCell.html">VoxelCell</a></li><li data-name="VoxelContent"><a href="VoxelContent.html">VoxelContent</a></li><li data-name="VoxelPrimitive"><a href="VoxelPrimitive.html">VoxelPrimitive</a></li><li data-name="VoxelProvider"><a href="VoxelProvider.html">VoxelProvider</a></li><li data-name="VoxelShapeType"><a href="global.html#VoxelShapeType">VoxelShapeType</a></li><li data-name="VRTheWorldTerrainProvider"><a href="VRTheWorldTerrainProvider.html">VRTheWorldTerrainProvider</a></li><li data-name="WallGeometry"><a href="WallGeometry.html">WallGeometry</a></li><li data-name="WallGeometryUpdater"><a href="WallGeometryUpdater.html">WallGeometryUpdater</a></li><li data-name="WallGraphics"><a href="WallGraphics.html">WallGraphics</a></li><li data-name="WallOutlineGeometry"><a href="WallOutlineGeometry.html">WallOutlineGeometry</a></li><li data-name="WebGLConstants"><a href="global.html#WebGLConstants">WebGLConstants</a></li><li data-name="WebGLOptions"><a href="global.html#WebGLOptions">WebGLOptions</a></li><li data-name="WebMapServiceImageryProvider"><a href="WebMapServiceImageryProvider.html">WebMapServiceImageryProvider</a></li><li data-name="WebMapTileServiceImageryProvider"><a href="WebMapTileServiceImageryProvider.html">WebMapTileServiceImageryProvider</a></li><li data-name="WebMercatorProjection"><a href="WebMercatorProjection.html">WebMercatorProjection</a></li><li data-name="WebMercatorTilingScheme"><a href="WebMercatorTilingScheme.html">WebMercatorTilingScheme</a></li><li data-name="WindingOrder"><a href="global.html#WindingOrder">WindingOrder</a></li><li data-name="writeTextToCanvas"><a href="global.html#writeTextToCanvas">writeTextToCanvas</a></li></ul><h5>packages/widgets</h5><ul><li data-name="Animation"><a href="Animation.html">Animation</a></li><li data-name="AnimationViewModel"><a href="AnimationViewModel.html">AnimationViewModel</a></li><li data-name="BaseLayerPicker"><a href="BaseLayerPicker.html">BaseLayerPicker</a></li><li data-name="BaseLayerPickerViewModel"><a href="BaseLayerPickerViewModel.html">BaseLayerPickerViewModel</a></li><li data-name="Cesium3DTilesInspector"><a href="Cesium3DTilesInspector.html">Cesium3DTilesInspector</a></li><li data-name="Cesium3DTilesInspectorViewModel"><a href="Cesium3DTilesInspectorViewModel.html">Cesium3DTilesInspectorViewModel</a></li><li data-name="CesiumInspector"><a href="CesiumInspector.html">CesiumInspector</a></li><li data-name="CesiumInspectorViewModel"><a href="CesiumInspectorViewModel.html">CesiumInspectorViewModel</a></li><li data-name="ClockViewModel"><a href="ClockViewModel.html">ClockViewModel</a></li><li data-name="Command"><a href="Command.html">Command</a></li><li data-name="createCommand"><a href="global.html#createCommand">createCommand</a></li><li data-name="FullscreenButton"><a href="FullscreenButton.html">FullscreenButton</a></li><li data-name="FullscreenButtonViewModel"><a href="FullscreenButtonViewModel.html">FullscreenButtonViewModel</a></li><li data-name="Geocoder"><a href="Geocoder.html">Geocoder</a></li><li data-name="GeocoderViewModel"><a href="GeocoderViewModel.html">GeocoderViewModel</a></li><li data-name="HomeButton"><a href="HomeButton.html">HomeButton</a></li><li data-name="HomeButtonViewModel"><a href="HomeButtonViewModel.html">HomeButtonViewModel</a></li><li data-name="I3sBslExplorerViewModel"><a href="I3sBslExplorerViewModel.html">I3sBslExplorerViewModel</a></li><li data-name="I3SBuildingSceneLayerExplorer"><a href="I3SBuildingSceneLayerExplorer.html">I3SBuildingSceneLayerExplorer</a></li><li data-name="InfoBox"><a href="InfoBox.html">InfoBox</a></li><li data-name="InfoBoxViewModel"><a href="InfoBoxViewModel.html">InfoBoxViewModel</a></li><li data-name="NavigationHelpButton"><a href="NavigationHelpButton.html">NavigationHelpButton</a></li><li data-name="NavigationHelpButtonViewModel"><a href="NavigationHelpButtonViewModel.html">NavigationHelpButtonViewModel</a></li><li data-name="PerformanceWatchdog"><a href="PerformanceWatchdog.html">PerformanceWatchdog</a></li><li data-name="PerformanceWatchdogViewModel"><a href="PerformanceWatchdogViewModel.html">PerformanceWatchdogViewModel</a></li><li data-name="ProjectionPicker"><a href="ProjectionPicker.html">ProjectionPicker</a></li><li data-name="ProjectionPickerViewModel"><a href="ProjectionPickerViewModel.html">ProjectionPickerViewModel</a></li><li data-name="ProviderViewModel"><a href="ProviderViewModel.html">ProviderViewModel</a></li><li data-name="SceneModePicker"><a href="SceneModePicker.html">SceneModePicker</a></li><li data-name="SceneModePickerViewModel"><a href="SceneModePickerViewModel.html">SceneModePickerViewModel</a></li><li data-name="SelectionIndicator"><a href="SelectionIndicator.html">SelectionIndicator</a></li><li data-name="SelectionIndicatorViewModel"><a href="SelectionIndicatorViewModel.html">SelectionIndicatorViewModel</a></li><li data-name="SvgPathBindingHandler"><a href="SvgPathBindingHandler.html">SvgPathBindingHandler</a></li><li data-name="Timeline"><a href="Timeline.html">Timeline</a></li><li data-name="ToggleButtonViewModel"><a href="ToggleButtonViewModel.html">ToggleButtonViewModel</a></li><li data-name="Viewer"><a href="Viewer.html">Viewer</a></li><li data-name="viewerCesium3DTilesInspectorMixin"><a href="global.html#viewerCesium3DTilesInspectorMixin">viewerCesium3DTilesInspectorMixin</a></li><li data-name="viewerCesiumInspectorMixin"><a href="global.html#viewerCesiumInspectorMixin">viewerCesiumInspectorMixin</a></li><li data-name="viewerDragDropMixin"><a href="global.html#viewerDragDropMixin">viewerDragDropMixin</a></li><li data-name="viewerPerformanceWatchdogMixin"><a href="global.html#viewerPerformanceWatchdogMixin">viewerPerformanceWatchdogMixin</a></li><li data-name="viewerVoxelInspectorMixin"><a href="global.html#viewerVoxelInspectorMixin">viewerVoxelInspectorMixin</a></li><li data-name="VoxelInspector"><a href="VoxelInspector.html">VoxelInspector</a></li><li data-name="VoxelInspectorViewModel"><a href="VoxelInspectorViewModel.html">VoxelInspectorViewModel</a></li><li data-name="VRButton"><a href="VRButton.html">VRButton</a></li><li data-name="VRButtonViewModel"><a href="VRButtonViewModel.html">VRButtonViewModel</a></li></ul></div>
    </div>
</div>

<script>
if (window.frameElement) {
    document.body.className = 'embedded';

    var ele = document.createElement('a');
    ele.className = 'popout';
    ele.target = '_blank';
    ele.href = window.location.href;
    ele.title = 'Pop out';
    document.getElementById('main').appendChild(ele);
}

// Set targets on external links.  Sandcastle and GitHub shouldn't be embedded in any iframe.
Array.prototype.forEach.call(document.getElementsByTagName('a'), function(a) {
    if (/^https?:/i.test(a.getAttribute('href'))) {
        a.target='_blank';
    }
});
</script>

<script src="javascript/prism.js"></script>
<script src="javascript/cesiumDoc.js"></script>

</body>
</html>