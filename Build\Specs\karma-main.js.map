{"version": 3, "sources": ["external-cesium:Cesium", "../../Specs/addDefaultMatchers.js", "../../Specs/equals.js", "../../Specs/equalsMethodEqualityTester.js", "../../Specs/customizeJasmine.js", "../../Specs/karma-main.js"], "sourcesContent": ["module.exports = Cesium", "import {\n  Cartesian2,\n  defined,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>r,\n  FeatureDetection,\n  PrimitiveType,\n  Buffer,\n  BufferUsage,\n  ClearCommand,\n  DrawCommand,\n  ShaderProgram,\n  VertexArray,\n  Math as CesiumMath,\n} from \"@cesium/engine\";\nimport equals from \"./equals.js\";\n\nfunction createMissingFunctionMessageFunction(\n  item,\n  actualPrototype,\n  expectedInterfacePrototype,\n) {\n  return function () {\n    return `Expected function '${item}' to exist on ${actualPrototype.constructor.name} because it should implement interface ${expectedInterfacePrototype.constructor.name}.`;\n  };\n}\n\nfunction makeAsyncThrowFunction(debug, Type, name) {\n  if (debug) {\n    return function (util) {\n      return {\n        compare: function (actualPromise, message) {\n          // based on the built-in Jasmine toBeRejectedWithError async-matcher\n          if (!defined(actualPromise) || !defined(actualPromise.then)) {\n            throw new Error(\"Expected function to be called on a promise.\");\n          }\n\n          return actualPromise\n            .then(() => {\n              return {\n                pass: false,\n                message:\n                  \"Expected a promise to be rejected but it was resolved.\",\n              };\n            })\n            .catch((e) => {\n              let result = e instanceof Type || e.name === name;\n              if (defined(message)) {\n                if (typeof message === \"string\") {\n                  result = result && e.message === message;\n                } else {\n                  // if the expected message is a regular expression check it against the error message\n                  // this matches how the builtin .toRejectWithError(Error, /message/) works\n                  // https://github.com/jasmine/jasmine/blob/main/src/core/matchers/toThrowError.js\n                  result = result && message.test(e.message);\n                }\n              }\n              return {\n                pass: result,\n                message: result\n                  ? `Expected a promise to be rejected with ${name}.`\n                  : `Expected a promise to be rejected with ${\n                      defined(message) ? `${name}: ${message}` : name\n                    }, but it was rejected with ${e}`,\n              };\n            });\n        },\n      };\n    };\n  }\n\n  return function () {\n    return {\n      compare: function (actualPromise) {\n        return Promise.resolve(actualPromise)\n          .then(() => {\n            return { pass: true };\n          })\n          .catch((e) => {\n            // Ignore any error\n            return { pass: true };\n          });\n      },\n      negativeCompare: function (actualPromise) {\n        return Promise.resolve(actualPromise)\n          .then(() => {\n            return { pass: true };\n          })\n          .catch((e) => {\n            // Ignore any error\n            return { pass: true };\n          });\n      },\n    };\n  };\n}\n\nfunction makeThrowFunction(debug, Type, name) {\n  if (debug) {\n    return function (util) {\n      return {\n        compare: function (actual, message) {\n          // based on the built-in Jasmine toThrow matcher\n          let result = false;\n          let exception;\n\n          if (typeof actual !== \"function\") {\n            throw new Error(\"Actual is not a function\");\n          }\n\n          try {\n            actual();\n          } catch (e) {\n            exception = e;\n          }\n\n          if (exception) {\n            result = exception instanceof Type || exception.name === name;\n          }\n          if (defined(message)) {\n            if (typeof message === \"string\") {\n              result = result && exception.message === message;\n            } else {\n              // if the expected message is a regular expression check it against the error message\n              // this matches how the builtin .toRejectWithError(Error, /message/) works\n              // https://github.com/jasmine/jasmine/blob/main/src/core/matchers/toThrowError.js\n              result = result && message.test(exception.message);\n            }\n          }\n\n          let testMessage;\n          if (result) {\n            testMessage = `Expected function not to throw ${name} , but it threw ${exception.message || exception}`;\n          } else {\n            testMessage = defined(message)\n              ? `Expected to throw with ${name}: ${message}, but it was thrown with ${exception}`\n              : `Expected function to throw with ${name}.`;\n          }\n\n          return {\n            pass: result,\n            message: testMessage,\n          };\n        },\n      };\n    };\n  }\n\n  return function () {\n    return {\n      compare: function (actual, expected) {\n        return { pass: true };\n      },\n      negativeCompare: function (actual, expected) {\n        return { pass: true };\n      },\n    };\n  };\n}\n\nfunction createDefaultMatchers(debug) {\n  return {\n    toBeBetween: function (util) {\n      return {\n        compare: function (actual, lower, upper) {\n          if (lower > upper) {\n            const tmp = upper;\n            upper = lower;\n            lower = tmp;\n          }\n          return { pass: actual >= lower && actual <= upper };\n        },\n      };\n    },\n\n    toStartWith: function (util) {\n      return {\n        compare: function (actual, expected) {\n          return { pass: actual.slice(0, expected.length) === expected };\n        },\n      };\n    },\n\n    toEndWith: function (util) {\n      return {\n        compare: function (actual, expected) {\n          return { pass: actual.slice(-expected.length) === expected };\n        },\n      };\n    },\n\n    toEqual: function (util) {\n      return {\n        compare: function (actual, expected) {\n          return {\n            pass: equals(util, actual, expected),\n          };\n        },\n      };\n    },\n\n    toEqualEpsilon: function (util) {\n      return {\n        compare: function (actual, expected, epsilon) {\n          function equalityTester(a, b) {\n            a = typedArrayToArray(a);\n            b = typedArrayToArray(b);\n            if (Array.isArray(a) && Array.isArray(b)) {\n              if (a.length !== b.length) {\n                return false;\n              }\n\n              for (let i = 0; i < a.length; ++i) {\n                if (!equalityTester(a[i], b[i])) {\n                  return false;\n                }\n              }\n\n              return true;\n            }\n\n            let to_run;\n            if (defined(a)) {\n              if (typeof a.equalsEpsilon === \"function\") {\n                return a.equalsEpsilon(b, epsilon);\n              } else if (a instanceof Object) {\n                // Check if the current object has a static function named 'equalsEpsilon'\n                to_run = Object.getPrototypeOf(a).constructor.equalsEpsilon;\n                if (typeof to_run === \"function\") {\n                  return to_run(a, b, epsilon);\n                }\n              }\n            }\n\n            if (defined(b)) {\n              if (typeof b.equalsEpsilon === \"function\") {\n                return b.equalsEpsilon(a, epsilon);\n              } else if (b instanceof Object) {\n                // Check if the current object has a static function named 'equalsEpsilon'\n                to_run = Object.getPrototypeOf(b).constructor.equalsEpsilon;\n                if (typeof to_run === \"function\") {\n                  return to_run(b, a, epsilon);\n                }\n              }\n            }\n\n            if (typeof a === \"number\" || typeof b === \"number\") {\n              return Math.abs(a - b) <= epsilon;\n            }\n\n            if (defined(a) && defined(b)) {\n              const keys = Object.keys(a);\n              for (let i = 0; i < keys.length; i++) {\n                if (!b.hasOwnProperty(keys[i])) {\n                  return false;\n                }\n                const aVal = a[keys[i]];\n                const bVal = b[keys[i]];\n                if (!equalityTester(aVal, bVal)) {\n                  return false;\n                }\n              }\n              return true;\n            }\n\n            return equals(util, a, b);\n          }\n\n          const result = equalityTester(actual, expected);\n          return { pass: result };\n        },\n      };\n    },\n\n    toConformToInterface: function (util) {\n      return {\n        compare: function (actual, expectedInterface) {\n          // All function properties on the prototype should also exist on the actual's prototype.\n          const actualPrototype = actual.prototype;\n          const expectedInterfacePrototype = expectedInterface.prototype;\n\n          for (const item in expectedInterfacePrototype) {\n            if (\n              expectedInterfacePrototype.hasOwnProperty(item) &&\n              typeof expectedInterfacePrototype[item] === \"function\" &&\n              !actualPrototype.hasOwnProperty(item)\n            ) {\n              return {\n                pass: false,\n                message: createMissingFunctionMessageFunction(\n                  item,\n                  actualPrototype,\n                  expectedInterfacePrototype,\n                ),\n              };\n            }\n          }\n\n          return { pass: true };\n        },\n      };\n    },\n\n    toRender: function (util) {\n      return {\n        compare: function (actual, expected) {\n          return renderEquals(util, actual, expected, true);\n        },\n      };\n    },\n\n    notToRender: function (util) {\n      return {\n        compare: function (actual, expected) {\n          return renderEquals(util, actual, expected, false);\n        },\n      };\n    },\n\n    toRenderAndCall: function (util) {\n      return {\n        compare: function (actual, expected) {\n          const actualRgba = renderAndReadPixels(actual);\n\n          const webglStub = !!window.webglStub;\n          if (!webglStub) {\n            // The callback may have expectations that fail, which still makes the\n            // spec fail, as we desired, even though this matcher sets pass to true.\n            const callback = expected;\n            callback(actualRgba);\n          }\n\n          return {\n            pass: true,\n          };\n        },\n      };\n    },\n\n    toRenderPixelCountAndCall: function (util) {\n      return {\n        compare: function (actual, expected) {\n          const actualRgba = renderAndReadPixels(actual);\n\n          const webglStub = !!window.webglStub;\n          if (!webglStub) {\n            // The callback may have expectations that fail, which still makes the\n            // spec fail, as we desired, even though this matcher sets pass to true.\n            const callback = expected;\n            callback(countRenderedPixels(actualRgba));\n          }\n\n          return {\n            pass: true,\n          };\n        },\n      };\n    },\n\n    toPickPrimitive: function (util) {\n      return {\n        compare: function (actual, expected, x, y, width, height) {\n          return pickPrimitiveEquals(actual, expected, x, y, width, height);\n        },\n      };\n    },\n\n    notToPick: function (util) {\n      return {\n        compare: function (actual, x, y, width, height) {\n          return pickPrimitiveEquals(actual, undefined, x, y, width, height);\n        },\n      };\n    },\n\n    toDrillPickPrimitive: function (util) {\n      return {\n        compare: function (actual, expected, x, y, width, height) {\n          return drillPickPrimitiveEquals(actual, 1, x, y, width, height);\n        },\n      };\n    },\n\n    notToDrillPick: function (util) {\n      return {\n        compare: function (actual, x, y, width, height) {\n          return drillPickPrimitiveEquals(actual, 0, x, y, width, height);\n        },\n      };\n    },\n\n    toPickAndCall: function (util) {\n      return {\n        compare: function (actual, expected, args) {\n          const scene = actual;\n          const result = scene.pick(args ?? new Cartesian2(0, 0));\n\n          const webglStub = !!window.webglStub;\n          if (!webglStub) {\n            // The callback may have expectations that fail, which still makes the\n            // spec fail, as we desired, even though this matcher sets pass to true.\n            const callback = expected;\n            callback(result);\n          }\n\n          return {\n            pass: true,\n          };\n        },\n      };\n    },\n\n    toPickVoxelAndCall: function (util) {\n      return {\n        compare: function (actual, expected, args) {\n          const scene = actual;\n          const result = scene.pickVoxel(args ?? new Cartesian2(0, 0));\n\n          const webglStub = !!window.webglStub;\n          if (!webglStub) {\n            // The callback may have expectations that fail, which still makes the\n            // spec fail, as we desired, even though this matcher sets pass to true.\n            const callback = expected;\n            callback(result);\n          }\n\n          return {\n            pass: true,\n          };\n        },\n      };\n    },\n\n    toDrillPickAndCall: function (util) {\n      return {\n        compare: function (actual, expected, limit) {\n          const scene = actual;\n          const pickedObjects = scene.drillPick(new Cartesian2(0, 0), limit);\n\n          const webglStub = !!window.webglStub;\n          if (!webglStub) {\n            // The callback may have expectations that fail, which still makes the\n            // spec fail, as we desired, even though this matcher sets pass to true.\n            const callback = expected;\n            callback(pickedObjects);\n          }\n\n          return {\n            pass: true,\n          };\n        },\n      };\n    },\n\n    toPickFromRayAndCall: function (util) {\n      return {\n        compare: function (actual, expected, ray, objectsToExclude, width) {\n          const scene = actual;\n          const result = scene.pickFromRay(ray, objectsToExclude, width);\n\n          const webglStub = !!window.webglStub;\n          if (!webglStub) {\n            // The callback may have expectations that fail, which still makes the\n            // spec fail, as we desired, even though this matcher sets pass to true.\n            const callback = expected;\n            callback(result);\n          }\n\n          return {\n            pass: true,\n          };\n        },\n      };\n    },\n\n    toDrillPickFromRayAndCall: function (util) {\n      return {\n        compare: function (\n          actual,\n          expected,\n          ray,\n          limit,\n          objectsToExclude,\n          width,\n        ) {\n          const scene = actual;\n          const results = scene.drillPickFromRay(\n            ray,\n            limit,\n            objectsToExclude,\n            width,\n          );\n\n          const webglStub = !!window.webglStub;\n          if (!webglStub) {\n            // The callback may have expectations that fail, which still makes the\n            // spec fail, as we desired, even though this matcher sets pass to true.\n            const callback = expected;\n            callback(results);\n          }\n\n          return {\n            pass: true,\n          };\n        },\n      };\n    },\n\n    toSampleHeightAndCall: function (util) {\n      return {\n        compare: function (\n          actual,\n          expected,\n          position,\n          objectsToExclude,\n          width,\n        ) {\n          const scene = actual;\n          const results = scene.sampleHeight(position, objectsToExclude, width);\n\n          const webglStub = !!window.webglStub;\n          if (!webglStub) {\n            // The callback may have expectations that fail, which still makes the\n            // spec fail, as we desired, even though this matcher sets pass to true.\n            const callback = expected;\n            callback(results);\n          }\n\n          return {\n            pass: true,\n          };\n        },\n      };\n    },\n\n    toClampToHeightAndCall: function (util) {\n      return {\n        compare: function (\n          actual,\n          expected,\n          cartesian,\n          objectsToExclude,\n          width,\n        ) {\n          const scene = actual;\n          const results = scene.clampToHeight(\n            cartesian,\n            objectsToExclude,\n            width,\n          );\n\n          const webglStub = !!window.webglStub;\n          if (!webglStub) {\n            // The callback may have expectations that fail, which still makes the\n            // spec fail, as we desired, even though this matcher sets pass to true.\n            const callback = expected;\n            callback(results);\n          }\n\n          return {\n            pass: true,\n          };\n        },\n      };\n    },\n\n    toPickPositionAndCall: function (util) {\n      return {\n        compare: function (actual, expected, x, y) {\n          const scene = actual;\n          const canvas = scene.canvas;\n          x = x ?? canvas.clientWidth / 2;\n          y = y ?? canvas.clientHeight / 2;\n          const result = scene.pickPosition(new Cartesian2(x, y));\n\n          const webglStub = !!window.webglStub;\n          if (!webglStub) {\n            // The callback may have expectations that fail, which still makes the\n            // spec fail, as we desired, even though this matcher sets pass to true.\n            const callback = expected;\n            callback(result);\n          }\n\n          return {\n            pass: true,\n          };\n        },\n      };\n    },\n\n    toReadPixels: function (util) {\n      return {\n        compare: function (actual, expected) {\n          let context;\n          let framebuffer;\n          let epsilon = 0;\n\n          const options = actual;\n          if (defined(options.context)) {\n            // options were passed to to a framebuffer\n            context = options.context;\n            framebuffer = options.framebuffer;\n            epsilon = options.epsilon ?? epsilon;\n          } else {\n            context = options;\n          }\n\n          const rgba = context.readPixels({\n            framebuffer: framebuffer,\n          });\n\n          let pass = true;\n          let message;\n\n          const webglStub = !!window.webglStub;\n          if (!webglStub) {\n            if (\n              !CesiumMath.equalsEpsilon(rgba[0], expected[0], 0, epsilon) ||\n              !CesiumMath.equalsEpsilon(rgba[1], expected[1], 0, epsilon) ||\n              !CesiumMath.equalsEpsilon(rgba[2], expected[2], 0, epsilon) ||\n              !CesiumMath.equalsEpsilon(rgba[3], expected[3], 0, epsilon)\n            ) {\n              pass = false;\n              if (epsilon === 0) {\n                message = `Expected context to render ${expected}, but rendered: ${rgba}`;\n              } else {\n                message = `Expected context to render ${expected} with epsilon = ${epsilon}, but rendered: ${rgba}`;\n              }\n            }\n          }\n\n          return {\n            pass: pass,\n            message: message,\n          };\n        },\n      };\n    },\n\n    notToReadPixels: function (util) {\n      return {\n        compare: function (actual, expected) {\n          const context = actual;\n          const rgba = context.readPixels();\n\n          let pass = true;\n          let message;\n\n          const webglStub = !!window.webglStub;\n          if (!webglStub) {\n            if (\n              rgba[0] === expected[0] &&\n              rgba[1] === expected[1] &&\n              rgba[2] === expected[2] &&\n              rgba[3] === expected[3]\n            ) {\n              pass = false;\n              message = `Expected context not to render ${expected}, but rendered: ${rgba}`;\n            }\n          }\n\n          return {\n            pass: pass,\n            message: message,\n          };\n        },\n      };\n    },\n\n    contextToRenderAndCall: function (util) {\n      return {\n        compare: function (actual, expected) {\n          const actualRgba = contextRenderAndReadPixels(actual).color;\n\n          const webglStub = !!window.webglStub;\n          if (!webglStub) {\n            // The callback may have expectations that fail, which still makes the\n            // spec fail, as we desired, even though this matcher sets pass to true.\n            const callback = expected;\n            callback(actualRgba);\n          }\n\n          return {\n            pass: true,\n          };\n        },\n      };\n    },\n\n    contextToRender: function (util) {\n      return {\n        compare: function (actual, expected) {\n          return expectContextToRender(actual, expected, true);\n        },\n      };\n    },\n\n    notContextToRender: function (util) {\n      return {\n        compare: function (actual, expected) {\n          return expectContextToRender(actual, expected, false);\n        },\n      };\n    },\n\n    toBeImageOrImageBitmap: function (util) {\n      return {\n        compare: function (actual) {\n          if (typeof createImageBitmap !== \"function\") {\n            return {\n              pass: actual instanceof Image,\n            };\n          }\n\n          return {\n            pass: actual instanceof ImageBitmap || actual instanceof Image,\n          };\n        },\n      };\n    },\n\n    toThrowDeveloperError: makeThrowFunction(\n      debug,\n      DeveloperError,\n      \"DeveloperError\",\n    ),\n  };\n}\n\nfunction createDefaultAsyncMatchers(debug) {\n  return {\n    toBeRejectedWithDeveloperError: makeAsyncThrowFunction(\n      debug,\n      DeveloperError,\n      \"DeveloperError\",\n    ),\n  };\n}\n\nfunction countRenderedPixels(rgba) {\n  const pixelCount = rgba.length / 4;\n  let count = 0;\n  for (let i = 0; i < pixelCount; i++) {\n    const index = i * 4;\n    if (\n      rgba[index] !== 0 ||\n      rgba[index + 1] !== 0 ||\n      rgba[index + 2] !== 0 ||\n      rgba[index + 3] !== 255\n    ) {\n      count++;\n    }\n  }\n  return count;\n}\n\nfunction renderAndReadPixels(options) {\n  let scene;\n\n  if (defined(options.scene)) {\n    // options were passed to render the scene at a given time or prime shadow map\n    scene = options.scene;\n    const time = options.time;\n\n    scene.initializeFrame();\n    if (defined(options.primeShadowMap)) {\n      scene.render(time); // Computes shadow near/far for next frame\n    }\n    scene.render(time);\n  } else {\n    scene = options;\n    scene.initializeFrame();\n    scene.render();\n  }\n\n  return scene.context.readPixels();\n}\n\nfunction isTypedArray(o) {\n  return FeatureDetection.typedArrayTypes.some(function (type) {\n    return o instanceof type;\n  });\n}\n\nfunction typedArrayToArray(array) {\n  if (isTypedArray(array)) {\n    return Array.prototype.slice.call(array, 0);\n  }\n  return array;\n}\n\nfunction renderEquals(util, actual, expected, expectEqual) {\n  const actualRgba = renderAndReadPixels(actual);\n\n  // When the WebGL stub is used, all WebGL function calls are noops so\n  // the expectation is not verified.  This allows running all the WebGL\n  // tests, to exercise as much Cesium code as possible, even if the system\n  // doesn't have a WebGL implementation or a reliable one.\n  if (!!window.webglStub) {\n    return {\n      pass: true,\n    };\n  }\n\n  const eq = equals(util, actualRgba, expected);\n  const pass = expectEqual ? eq : !eq;\n\n  let message;\n  if (!pass) {\n    message = `Expected ${\n      expectEqual ? \"\" : \"not \"\n    }to render [${typedArrayToArray(\n      expected,\n    )}], but actually rendered [${typedArrayToArray(actualRgba)}].`;\n  }\n\n  return {\n    pass: pass,\n    message: message,\n  };\n}\n\nfunction pickPrimitiveEquals(actual, expected, x, y, width, height) {\n  const scene = actual;\n  const windowPosition = new Cartesian2(x, y);\n  const result = scene.pick(windowPosition, width, height);\n\n  if (!!window.webglStub) {\n    return {\n      pass: true,\n    };\n  }\n\n  let pass = true;\n  let message;\n\n  if (defined(expected)) {\n    pass = result.primitive === expected;\n  } else {\n    pass = !defined(result);\n  }\n\n  if (!pass) {\n    message = `Expected to pick ${expected}, but picked: ${result}`;\n  }\n\n  return {\n    pass: pass,\n    message: message,\n  };\n}\n\nfunction drillPickPrimitiveEquals(actual, expected, x, y, width, height) {\n  const scene = actual;\n  const windowPosition = new Cartesian2(x, y);\n  const result = scene.drillPick(windowPosition, undefined, width, height);\n\n  if (!!window.webglStub) {\n    return {\n      pass: true,\n    };\n  }\n\n  let pass = true;\n  let message;\n\n  if (defined(expected)) {\n    pass = result.length === expected;\n  } else {\n    pass = !defined(result);\n  }\n\n  if (!pass) {\n    message = `Expected to pick ${expected}, but picked: ${result}`;\n  }\n\n  return {\n    pass: pass,\n    message: message,\n  };\n}\n\nfunction contextRenderAndReadPixels(options) {\n  const context = options.context;\n  let vs = options.vertexShader;\n  const fs = options.fragmentShader;\n  let sp = options.shaderProgram;\n  const uniformMap = options.uniformMap;\n  const modelMatrix = options.modelMatrix;\n  const depth = options.depth ?? 0.0;\n  const clear = options.clear ?? true;\n  let clearColor;\n\n  if (!defined(context)) {\n    throw new DeveloperError(\"options.context is required.\");\n  }\n\n  if (!defined(fs) && !defined(sp)) {\n    throw new DeveloperError(\n      \"options.fragmentShader or options.shaderProgram is required.\",\n    );\n  }\n\n  if (defined(fs) && defined(sp)) {\n    throw new DeveloperError(\n      \"Both options.fragmentShader and options.shaderProgram can not be used at the same time.\",\n    );\n  }\n\n  if (defined(vs) && defined(sp)) {\n    throw new DeveloperError(\n      \"Both options.vertexShader and options.shaderProgram can not be used at the same time.\",\n    );\n  }\n\n  if (!defined(sp)) {\n    if (!defined(vs)) {\n      vs =\n        \"in vec4 position; void main() { gl_PointSize = 1.0; gl_Position = position; }\";\n    }\n    sp = ShaderProgram.fromCache({\n      context: context,\n      vertexShaderSource: vs,\n      fragmentShaderSource: fs,\n      attributeLocations: {\n        position: 0,\n      },\n    });\n  }\n\n  let va = new VertexArray({\n    context: context,\n    attributes: [\n      {\n        index: 0,\n        vertexBuffer: Buffer.createVertexBuffer({\n          context: context,\n          typedArray: new Float32Array([0.0, 0.0, depth, 1.0]),\n          usage: BufferUsage.STATIC_DRAW,\n        }),\n        componentsPerAttribute: 4,\n      },\n    ],\n  });\n\n  if (clear) {\n    ClearCommand.ALL.execute(context);\n    clearColor = context.readPixels();\n  }\n\n  const command = new DrawCommand({\n    primitiveType: PrimitiveType.POINTS,\n    shaderProgram: sp,\n    vertexArray: va,\n    uniformMap: uniformMap,\n    modelMatrix: modelMatrix,\n  });\n\n  command.execute(context);\n  const rgba = context.readPixels();\n\n  sp = sp.destroy();\n  va = va.destroy();\n\n  return {\n    color: rgba,\n    clearColor: clearColor,\n  };\n}\n\nfunction expectContextToRender(actual, expected, expectEqual) {\n  const options = actual;\n  const context = options.context;\n  const clear = options.clear ?? true;\n  const epsilon = options.epsilon ?? 0;\n\n  if (!defined(expected)) {\n    expected = [255, 255, 255, 255];\n  }\n\n  const webglStub = !!window.webglStub;\n\n  const output = contextRenderAndReadPixels(options);\n\n  if (clear) {\n    const clearedRgba = output.clearColor;\n    if (!webglStub) {\n      const expectedAlpha = context.options.webgl.alpha ? 0 : 255;\n      if (\n        clearedRgba[0] !== 0 ||\n        clearedRgba[1] !== 0 ||\n        clearedRgba[2] !== 0 ||\n        clearedRgba[3] !== expectedAlpha\n      ) {\n        return {\n          pass: false,\n          message: `After clearing the framebuffer, expected context to render [0, 0, 0, ${expectedAlpha}], but rendered: ${clearedRgba}`,\n        };\n      }\n    }\n  }\n\n  const rgba = output.color;\n\n  if (!webglStub) {\n    if (expectEqual) {\n      if (\n        !CesiumMath.equalsEpsilon(rgba[0], expected[0], 0, epsilon) ||\n        !CesiumMath.equalsEpsilon(rgba[1], expected[1], 0, epsilon) ||\n        !CesiumMath.equalsEpsilon(rgba[2], expected[2], 0, epsilon) ||\n        !CesiumMath.equalsEpsilon(rgba[3], expected[3], 0, epsilon)\n      ) {\n        return {\n          pass: false,\n          message: `Expected context to render ${expected}, but rendered: ${rgba}`,\n        };\n      }\n    } else if (\n      CesiumMath.equalsEpsilon(rgba[0], expected[0], 0, epsilon) &&\n      CesiumMath.equalsEpsilon(rgba[1], expected[1], 0, epsilon) &&\n      CesiumMath.equalsEpsilon(rgba[2], expected[2], 0, epsilon) &&\n      CesiumMath.equalsEpsilon(rgba[3], expected[3], 0, epsilon)\n    ) {\n      return {\n        pass: false,\n        message: `Expected context not to render ${expected}, but rendered: ${rgba}`,\n      };\n    }\n  }\n\n  return {\n    pass: true,\n  };\n}\n\nfunction addDefaultMatchers(debug) {\n  return function () {\n    this.addMatchers(createDefaultMatchers(debug));\n    this.addAsyncMatchers(createDefaultAsyncMatchers(debug));\n  };\n}\nexport default addDefaultMatchers;\n", "import { FeatureDetection } from \"@cesium/engine\";\n\nfunction isTypedArray(o) {\n  return FeatureDetection.typedArrayTypes.some(function (type) {\n    return o instanceof type;\n  });\n}\n\nfunction typedArrayToArray(array) {\n  if (array !== null && typeof array === \"object\" && isTypedArray(array)) {\n    return Array.prototype.slice.call(array, 0);\n  }\n  return array;\n}\n\nfunction equals(util, a, b) {\n  a = typedArrayToArray(a);\n  b = typedArrayToArray(b);\n  return util.equals(a, b);\n}\nexport default equals;\n", "import { defined } from \"@cesium/engine\";\n\nfunction equalsMethodEqualityTester(a, b) {\n  let to_run;\n  // if either a or b have an equals method, call it.\n  if (a !== null && defined(a)) {\n    if (typeof a.equals === \"function\") {\n      return a.equals(b);\n    } else if (a instanceof Object) {\n      // Check if the current object has a static function named 'equals'\n      to_run = Object.getPrototypeOf(a).constructor.equals;\n      if (typeof to_run === \"function\") {\n        return to_run(a, b);\n      }\n    }\n  }\n\n  if (b !== null && defined(b)) {\n    if (typeof b.equals === \"function\") {\n      return b.equals(a);\n    } else if (b instanceof Object) {\n      // Check if the current object has a static function named 'equals'\n      to_run = Object.getPrototypeOf(b).constructor.equals;\n      if (typeof to_run === \"function\") {\n        return to_run(b, a);\n      }\n    }\n  }\n\n  // fall back to default equality checks.\n  return undefined;\n}\nexport default equalsMethodEqualityTester;\n", "import addDefaultMatchers from \"./addDefaultMatchers.js\";\nimport equalsMethodEqualityTester from \"./equalsMethodEqualityTester.js\";\n\nfunction customizeJasmine(\n  env,\n  includedCategory,\n  excludedCategory,\n  webglValidation,\n  webglStub,\n  release,\n  debugCanvasWidth,\n  debugCanvasHeight,\n) {\n  // set this for uniform test resolution across devices\n  window.devicePixelRatio = 1;\n\n  window.specsUsingRelease = release;\n\n  const originalDescribe = window.describe;\n\n  window.describe = function (name, suite, category) {\n    // exclude this spec if we're filtering by category and it's not the selected category\n    // otherwise if we have an excluded category, exclude this test if the category of this spec matches\n    if (\n      includedCategory &&\n      includedCategory !== \"\" &&\n      includedCategory !== \"none\" &&\n      category !== includedCategory\n    ) {\n      window.xdescribe(name, suite);\n    } else if (\n      excludedCategory &&\n      excludedCategory !== \"\" &&\n      category === excludedCategory\n    ) {\n      window.xdescribe(name, suite);\n    } else {\n      originalDescribe(name, suite);\n    }\n  };\n\n  if (webglValidation) {\n    window.webglValidation = true;\n  }\n\n  if (webglStub) {\n    window.webglStub = true;\n  }\n\n  window.debugCanvasWidth = debugCanvasWidth;\n  window.debugCanvasHeight = debugCanvasHeight;\n\n  env.beforeEach(function () {\n    addDefaultMatchers(!release).call(env);\n    env.addCustomEqualityTester(equalsMethodEqualityTester);\n  });\n}\nexport default customizeJasmine;\n", "/*global __karma__*/\nimport customizeJasmine from \"./customizeJasmine.js\";\n\nlet includeCategory = \"\";\nlet excludeCategory = \"\";\nlet webglValidation = false;\nlet webglStub = false;\nlet release = false;\nlet debugCanvasWidth;\nlet debugCanvasHeight;\n\nif (__karma__.config.args) {\n  includeCategory = __karma__.config.args[0];\n  excludeCategory = __karma__.config.args[1];\n  webglValidation = __karma__.config.args[4];\n  webglStub = __karma__.config.args[5];\n  release = __karma__.config.args[6];\n  debugCanvasWidth = __karma__.config.args[7];\n  debugCanvasHeight = __karma__.config.args[8];\n}\n\nif (release) {\n  window.CESIUM_BASE_URL = \"base/Build/Cesium\";\n} else {\n  window.CESIUM_BASE_URL = \"base/Build/CesiumUnminified\";\n}\n\njasmine.DEFAULT_TIMEOUT_INTERVAL = 30000;\ncustomizeJasmine(\n  jasmine.getEnv(),\n  includeCategory,\n  excludeCategory,\n  webglValidation,\n  webglStub,\n  release,\n  debugCanvasWidth,\n  debugCanvasHeight,\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB,IAAAA,iBAaO;;;ACbP,oBAAiC;AAEjC,SAAS,aAAa,GAAG;AACvB,SAAO,+BAAiB,gBAAgB,KAAK,SAAU,MAAM;AAC3D,WAAO,aAAa;AAAA,EACtB,CAAC;AACH;AAEA,SAAS,kBAAkB,OAAO;AAChC,MAAI,UAAU,QAAQ,OAAO,UAAU,YAAY,aAAa,KAAK,GAAG;AACtE,WAAO,MAAM,UAAU,MAAM,KAAK,OAAO,CAAC;AAAA,EAC5C;AACA,SAAO;AACT;AAEA,SAAS,OAAO,MAAM,GAAG,GAAG;AAC1B,MAAI,kBAAkB,CAAC;AACvB,MAAI,kBAAkB,CAAC;AACvB,SAAO,KAAK,OAAO,GAAG,CAAC;AACzB;AACA,IAAO,iBAAQ;;;ADJf,SAAS,qCACP,MACA,iBACA,4BACA;AACA,SAAO,WAAY;AACjB,WAAO,sBAAsB,IAAI,iBAAiB,gBAAgB,YAAY,IAAI,0CAA0C,2BAA2B,YAAY,IAAI;AAAA,EACzK;AACF;AAEA,SAAS,uBAAuB,OAAO,MAAM,MAAM;AACjD,MAAI,OAAO;AACT,WAAO,SAAU,MAAM;AACrB,aAAO;AAAA,QACL,SAAS,SAAU,eAAe,SAAS;AAEzC,cAAI,KAAC,wBAAQ,aAAa,KAAK,KAAC,wBAAQ,cAAc,IAAI,GAAG;AAC3D,kBAAM,IAAI,MAAM,8CAA8C;AAAA,UAChE;AAEA,iBAAO,cACJ,KAAK,MAAM;AACV,mBAAO;AAAA,cACL,MAAM;AAAA,cACN,SACE;AAAA,YACJ;AAAA,UACF,CAAC,EACA,MAAM,CAAC,MAAM;AACZ,gBAAI,SAAS,aAAa,QAAQ,EAAE,SAAS;AAC7C,oBAAI,wBAAQ,OAAO,GAAG;AACpB,kBAAI,OAAO,YAAY,UAAU;AAC/B,yBAAS,UAAU,EAAE,YAAY;AAAA,cACnC,OAAO;AAIL,yBAAS,UAAU,QAAQ,KAAK,EAAE,OAAO;AAAA,cAC3C;AAAA,YACF;AACA,mBAAO;AAAA,cACL,MAAM;AAAA,cACN,SAAS,SACL,0CAA0C,IAAI,MAC9C,8CACE,wBAAQ,OAAO,IAAI,GAAG,IAAI,KAAK,OAAO,KAAK,IAC7C,8BAA8B,CAAC;AAAA,YACrC;AAAA,UACF,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAY;AACjB,WAAO;AAAA,MACL,SAAS,SAAU,eAAe;AAChC,eAAO,QAAQ,QAAQ,aAAa,EACjC,KAAK,MAAM;AACV,iBAAO,EAAE,MAAM,KAAK;AAAA,QACtB,CAAC,EACA,MAAM,CAAC,MAAM;AAEZ,iBAAO,EAAE,MAAM,KAAK;AAAA,QACtB,CAAC;AAAA,MACL;AAAA,MACA,iBAAiB,SAAU,eAAe;AACxC,eAAO,QAAQ,QAAQ,aAAa,EACjC,KAAK,MAAM;AACV,iBAAO,EAAE,MAAM,KAAK;AAAA,QACtB,CAAC,EACA,MAAM,CAAC,MAAM;AAEZ,iBAAO,EAAE,MAAM,KAAK;AAAA,QACtB,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,OAAO,MAAM,MAAM;AAC5C,MAAI,OAAO;AACT,WAAO,SAAU,MAAM;AACrB,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,SAAS;AAElC,cAAI,SAAS;AACb,cAAI;AAEJ,cAAI,OAAO,WAAW,YAAY;AAChC,kBAAM,IAAI,MAAM,0BAA0B;AAAA,UAC5C;AAEA,cAAI;AACF,mBAAO;AAAA,UACT,SAAS,GAAG;AACV,wBAAY;AAAA,UACd;AAEA,cAAI,WAAW;AACb,qBAAS,qBAAqB,QAAQ,UAAU,SAAS;AAAA,UAC3D;AACA,kBAAI,wBAAQ,OAAO,GAAG;AACpB,gBAAI,OAAO,YAAY,UAAU;AAC/B,uBAAS,UAAU,UAAU,YAAY;AAAA,YAC3C,OAAO;AAIL,uBAAS,UAAU,QAAQ,KAAK,UAAU,OAAO;AAAA,YACnD;AAAA,UACF;AAEA,cAAI;AACJ,cAAI,QAAQ;AACV,0BAAc,kCAAkC,IAAI,mBAAmB,UAAU,WAAW,SAAS;AAAA,UACvG,OAAO;AACL,8BAAc,wBAAQ,OAAO,IACzB,0BAA0B,IAAI,KAAK,OAAO,4BAA4B,SAAS,KAC/E,mCAAmC,IAAI;AAAA,UAC7C;AAEA,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO,WAAY;AACjB,WAAO;AAAA,MACL,SAAS,SAAU,QAAQ,UAAU;AACnC,eAAO,EAAE,MAAM,KAAK;AAAA,MACtB;AAAA,MACA,iBAAiB,SAAU,QAAQ,UAAU;AAC3C,eAAO,EAAE,MAAM,KAAK;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,sBAAsB,OAAO;AACpC,SAAO;AAAA,IACL,aAAa,SAAU,MAAM;AAC3B,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,OAAO,OAAO;AACvC,cAAI,QAAQ,OAAO;AACjB,kBAAM,MAAM;AACZ,oBAAQ;AACR,oBAAQ;AAAA,UACV;AACA,iBAAO,EAAE,MAAM,UAAU,SAAS,UAAU,MAAM;AAAA,QACpD;AAAA,MACF;AAAA,IACF;AAAA,IAEA,aAAa,SAAU,MAAM;AAC3B,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU;AACnC,iBAAO,EAAE,MAAM,OAAO,MAAM,GAAG,SAAS,MAAM,MAAM,SAAS;AAAA,QAC/D;AAAA,MACF;AAAA,IACF;AAAA,IAEA,WAAW,SAAU,MAAM;AACzB,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU;AACnC,iBAAO,EAAE,MAAM,OAAO,MAAM,CAAC,SAAS,MAAM,MAAM,SAAS;AAAA,QAC7D;AAAA,MACF;AAAA,IACF;AAAA,IAEA,SAAS,SAAU,MAAM;AACvB,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU;AACnC,iBAAO;AAAA,YACL,MAAM,eAAO,MAAM,QAAQ,QAAQ;AAAA,UACrC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,gBAAgB,SAAU,MAAM;AAC9B,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU,SAAS;AAC5C,mBAAS,eAAe,GAAG,GAAG;AAC5B,gBAAIC,mBAAkB,CAAC;AACvB,gBAAIA,mBAAkB,CAAC;AACvB,gBAAI,MAAM,QAAQ,CAAC,KAAK,MAAM,QAAQ,CAAC,GAAG;AACxC,kBAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,uBAAO;AAAA,cACT;AAEA,uBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AACjC,oBAAI,CAAC,eAAe,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG;AAC/B,yBAAO;AAAA,gBACT;AAAA,cACF;AAEA,qBAAO;AAAA,YACT;AAEA,gBAAI;AACJ,oBAAI,wBAAQ,CAAC,GAAG;AACd,kBAAI,OAAO,EAAE,kBAAkB,YAAY;AACzC,uBAAO,EAAE,cAAc,GAAG,OAAO;AAAA,cACnC,WAAW,aAAa,QAAQ;AAE9B,yBAAS,OAAO,eAAe,CAAC,EAAE,YAAY;AAC9C,oBAAI,OAAO,WAAW,YAAY;AAChC,yBAAO,OAAO,GAAG,GAAG,OAAO;AAAA,gBAC7B;AAAA,cACF;AAAA,YACF;AAEA,oBAAI,wBAAQ,CAAC,GAAG;AACd,kBAAI,OAAO,EAAE,kBAAkB,YAAY;AACzC,uBAAO,EAAE,cAAc,GAAG,OAAO;AAAA,cACnC,WAAW,aAAa,QAAQ;AAE9B,yBAAS,OAAO,eAAe,CAAC,EAAE,YAAY;AAC9C,oBAAI,OAAO,WAAW,YAAY;AAChC,yBAAO,OAAO,GAAG,GAAG,OAAO;AAAA,gBAC7B;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAClD,qBAAO,KAAK,IAAI,IAAI,CAAC,KAAK;AAAA,YAC5B;AAEA,oBAAI,wBAAQ,CAAC,SAAK,wBAAQ,CAAC,GAAG;AAC5B,oBAAM,OAAO,OAAO,KAAK,CAAC;AAC1B,uBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,oBAAI,CAAC,EAAE,eAAe,KAAK,CAAC,CAAC,GAAG;AAC9B,yBAAO;AAAA,gBACT;AACA,sBAAM,OAAO,EAAE,KAAK,CAAC,CAAC;AACtB,sBAAM,OAAO,EAAE,KAAK,CAAC,CAAC;AACtB,oBAAI,CAAC,eAAe,MAAM,IAAI,GAAG;AAC/B,yBAAO;AAAA,gBACT;AAAA,cACF;AACA,qBAAO;AAAA,YACT;AAEA,mBAAO,eAAO,MAAM,GAAG,CAAC;AAAA,UAC1B;AAEA,gBAAM,SAAS,eAAe,QAAQ,QAAQ;AAC9C,iBAAO,EAAE,MAAM,OAAO;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA,IAEA,sBAAsB,SAAU,MAAM;AACpC,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,mBAAmB;AAE5C,gBAAM,kBAAkB,OAAO;AAC/B,gBAAM,6BAA6B,kBAAkB;AAErD,qBAAW,QAAQ,4BAA4B;AAC7C,gBACE,2BAA2B,eAAe,IAAI,KAC9C,OAAO,2BAA2B,IAAI,MAAM,cAC5C,CAAC,gBAAgB,eAAe,IAAI,GACpC;AACA,qBAAO;AAAA,gBACL,MAAM;AAAA,gBACN,SAAS;AAAA,kBACP;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAEA,iBAAO,EAAE,MAAM,KAAK;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AAAA,IAEA,UAAU,SAAU,MAAM;AACxB,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU;AACnC,iBAAO,aAAa,MAAM,QAAQ,UAAU,IAAI;AAAA,QAClD;AAAA,MACF;AAAA,IACF;AAAA,IAEA,aAAa,SAAU,MAAM;AAC3B,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU;AACnC,iBAAO,aAAa,MAAM,QAAQ,UAAU,KAAK;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AAAA,IAEA,iBAAiB,SAAU,MAAM;AAC/B,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU;AACnC,gBAAM,aAAa,oBAAoB,MAAM;AAE7C,gBAAMC,aAAY,CAAC,CAAC,OAAO;AAC3B,cAAI,CAACA,YAAW;AAGd,kBAAM,WAAW;AACjB,qBAAS,UAAU;AAAA,UACrB;AAEA,iBAAO;AAAA,YACL,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,2BAA2B,SAAU,MAAM;AACzC,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU;AACnC,gBAAM,aAAa,oBAAoB,MAAM;AAE7C,gBAAMA,aAAY,CAAC,CAAC,OAAO;AAC3B,cAAI,CAACA,YAAW;AAGd,kBAAM,WAAW;AACjB,qBAAS,oBAAoB,UAAU,CAAC;AAAA,UAC1C;AAEA,iBAAO;AAAA,YACL,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,iBAAiB,SAAU,MAAM;AAC/B,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU,GAAG,GAAG,OAAO,QAAQ;AACxD,iBAAO,oBAAoB,QAAQ,UAAU,GAAG,GAAG,OAAO,MAAM;AAAA,QAClE;AAAA,MACF;AAAA,IACF;AAAA,IAEA,WAAW,SAAU,MAAM;AACzB,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,GAAG,GAAG,OAAO,QAAQ;AAC9C,iBAAO,oBAAoB,QAAQ,QAAW,GAAG,GAAG,OAAO,MAAM;AAAA,QACnE;AAAA,MACF;AAAA,IACF;AAAA,IAEA,sBAAsB,SAAU,MAAM;AACpC,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU,GAAG,GAAG,OAAO,QAAQ;AACxD,iBAAO,yBAAyB,QAAQ,GAAG,GAAG,GAAG,OAAO,MAAM;AAAA,QAChE;AAAA,MACF;AAAA,IACF;AAAA,IAEA,gBAAgB,SAAU,MAAM;AAC9B,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,GAAG,GAAG,OAAO,QAAQ;AAC9C,iBAAO,yBAAyB,QAAQ,GAAG,GAAG,GAAG,OAAO,MAAM;AAAA,QAChE;AAAA,MACF;AAAA,IACF;AAAA,IAEA,eAAe,SAAU,MAAM;AAC7B,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU,MAAM;AACzC,gBAAM,QAAQ;AACd,gBAAM,SAAS,MAAM,KAAK,QAAQ,IAAI,0BAAW,GAAG,CAAC,CAAC;AAEtD,gBAAMA,aAAY,CAAC,CAAC,OAAO;AAC3B,cAAI,CAACA,YAAW;AAGd,kBAAM,WAAW;AACjB,qBAAS,MAAM;AAAA,UACjB;AAEA,iBAAO;AAAA,YACL,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,oBAAoB,SAAU,MAAM;AAClC,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU,MAAM;AACzC,gBAAM,QAAQ;AACd,gBAAM,SAAS,MAAM,UAAU,QAAQ,IAAI,0BAAW,GAAG,CAAC,CAAC;AAE3D,gBAAMA,aAAY,CAAC,CAAC,OAAO;AAC3B,cAAI,CAACA,YAAW;AAGd,kBAAM,WAAW;AACjB,qBAAS,MAAM;AAAA,UACjB;AAEA,iBAAO;AAAA,YACL,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,oBAAoB,SAAU,MAAM;AAClC,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU,OAAO;AAC1C,gBAAM,QAAQ;AACd,gBAAM,gBAAgB,MAAM,UAAU,IAAI,0BAAW,GAAG,CAAC,GAAG,KAAK;AAEjE,gBAAMA,aAAY,CAAC,CAAC,OAAO;AAC3B,cAAI,CAACA,YAAW;AAGd,kBAAM,WAAW;AACjB,qBAAS,aAAa;AAAA,UACxB;AAEA,iBAAO;AAAA,YACL,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,sBAAsB,SAAU,MAAM;AACpC,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU,KAAK,kBAAkB,OAAO;AACjE,gBAAM,QAAQ;AACd,gBAAM,SAAS,MAAM,YAAY,KAAK,kBAAkB,KAAK;AAE7D,gBAAMA,aAAY,CAAC,CAAC,OAAO;AAC3B,cAAI,CAACA,YAAW;AAGd,kBAAM,WAAW;AACjB,qBAAS,MAAM;AAAA,UACjB;AAEA,iBAAO;AAAA,YACL,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,2BAA2B,SAAU,MAAM;AACzC,aAAO;AAAA,QACL,SAAS,SACP,QACA,UACA,KACA,OACA,kBACA,OACA;AACA,gBAAM,QAAQ;AACd,gBAAM,UAAU,MAAM;AAAA,YACpB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAEA,gBAAMA,aAAY,CAAC,CAAC,OAAO;AAC3B,cAAI,CAACA,YAAW;AAGd,kBAAM,WAAW;AACjB,qBAAS,OAAO;AAAA,UAClB;AAEA,iBAAO;AAAA,YACL,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,uBAAuB,SAAU,MAAM;AACrC,aAAO;AAAA,QACL,SAAS,SACP,QACA,UACA,UACA,kBACA,OACA;AACA,gBAAM,QAAQ;AACd,gBAAM,UAAU,MAAM,aAAa,UAAU,kBAAkB,KAAK;AAEpE,gBAAMA,aAAY,CAAC,CAAC,OAAO;AAC3B,cAAI,CAACA,YAAW;AAGd,kBAAM,WAAW;AACjB,qBAAS,OAAO;AAAA,UAClB;AAEA,iBAAO;AAAA,YACL,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,wBAAwB,SAAU,MAAM;AACtC,aAAO;AAAA,QACL,SAAS,SACP,QACA,UACA,WACA,kBACA,OACA;AACA,gBAAM,QAAQ;AACd,gBAAM,UAAU,MAAM;AAAA,YACpB;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAEA,gBAAMA,aAAY,CAAC,CAAC,OAAO;AAC3B,cAAI,CAACA,YAAW;AAGd,kBAAM,WAAW;AACjB,qBAAS,OAAO;AAAA,UAClB;AAEA,iBAAO;AAAA,YACL,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,uBAAuB,SAAU,MAAM;AACrC,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU,GAAG,GAAG;AACzC,gBAAM,QAAQ;AACd,gBAAM,SAAS,MAAM;AACrB,cAAI,KAAK,OAAO,cAAc;AAC9B,cAAI,KAAK,OAAO,eAAe;AAC/B,gBAAM,SAAS,MAAM,aAAa,IAAI,0BAAW,GAAG,CAAC,CAAC;AAEtD,gBAAMA,aAAY,CAAC,CAAC,OAAO;AAC3B,cAAI,CAACA,YAAW;AAGd,kBAAM,WAAW;AACjB,qBAAS,MAAM;AAAA,UACjB;AAEA,iBAAO;AAAA,YACL,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,cAAc,SAAU,MAAM;AAC5B,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU;AACnC,cAAI;AACJ,cAAI;AACJ,cAAI,UAAU;AAEd,gBAAM,UAAU;AAChB,kBAAI,wBAAQ,QAAQ,OAAO,GAAG;AAE5B,sBAAU,QAAQ;AAClB,0BAAc,QAAQ;AACtB,sBAAU,QAAQ,WAAW;AAAA,UAC/B,OAAO;AACL,sBAAU;AAAA,UACZ;AAEA,gBAAM,OAAO,QAAQ,WAAW;AAAA,YAC9B;AAAA,UACF,CAAC;AAED,cAAI,OAAO;AACX,cAAI;AAEJ,gBAAMA,aAAY,CAAC,CAAC,OAAO;AAC3B,cAAI,CAACA,YAAW;AACd,gBACE,CAAC,eAAAC,KAAW,cAAc,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,OAAO,KAC1D,CAAC,eAAAA,KAAW,cAAc,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,OAAO,KAC1D,CAAC,eAAAA,KAAW,cAAc,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,OAAO,KAC1D,CAAC,eAAAA,KAAW,cAAc,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,OAAO,GAC1D;AACA,qBAAO;AACP,kBAAI,YAAY,GAAG;AACjB,0BAAU,8BAA8B,QAAQ,mBAAmB,IAAI;AAAA,cACzE,OAAO;AACL,0BAAU,8BAA8B,QAAQ,mBAAmB,OAAO,mBAAmB,IAAI;AAAA,cACnG;AAAA,YACF;AAAA,UACF;AAEA,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,iBAAiB,SAAU,MAAM;AAC/B,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU;AACnC,gBAAM,UAAU;AAChB,gBAAM,OAAO,QAAQ,WAAW;AAEhC,cAAI,OAAO;AACX,cAAI;AAEJ,gBAAMD,aAAY,CAAC,CAAC,OAAO;AAC3B,cAAI,CAACA,YAAW;AACd,gBACE,KAAK,CAAC,MAAM,SAAS,CAAC,KACtB,KAAK,CAAC,MAAM,SAAS,CAAC,KACtB,KAAK,CAAC,MAAM,SAAS,CAAC,KACtB,KAAK,CAAC,MAAM,SAAS,CAAC,GACtB;AACA,qBAAO;AACP,wBAAU,kCAAkC,QAAQ,mBAAmB,IAAI;AAAA,YAC7E;AAAA,UACF;AAEA,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,wBAAwB,SAAU,MAAM;AACtC,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU;AACnC,gBAAM,aAAa,2BAA2B,MAAM,EAAE;AAEtD,gBAAMA,aAAY,CAAC,CAAC,OAAO;AAC3B,cAAI,CAACA,YAAW;AAGd,kBAAM,WAAW;AACjB,qBAAS,UAAU;AAAA,UACrB;AAEA,iBAAO;AAAA,YACL,MAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,iBAAiB,SAAU,MAAM;AAC/B,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU;AACnC,iBAAO,sBAAsB,QAAQ,UAAU,IAAI;AAAA,QACrD;AAAA,MACF;AAAA,IACF;AAAA,IAEA,oBAAoB,SAAU,MAAM;AAClC,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ,UAAU;AACnC,iBAAO,sBAAsB,QAAQ,UAAU,KAAK;AAAA,QACtD;AAAA,MACF;AAAA,IACF;AAAA,IAEA,wBAAwB,SAAU,MAAM;AACtC,aAAO;AAAA,QACL,SAAS,SAAU,QAAQ;AACzB,cAAI,OAAO,sBAAsB,YAAY;AAC3C,mBAAO;AAAA,cACL,MAAM,kBAAkB;AAAA,YAC1B;AAAA,UACF;AAEA,iBAAO;AAAA,YACL,MAAM,kBAAkB,eAAe,kBAAkB;AAAA,UAC3D;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IAEA,uBAAuB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,2BAA2B,OAAO;AACzC,SAAO;AAAA,IACL,gCAAgC;AAAA,MAC9B;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,oBAAoB,MAAM;AACjC,QAAM,aAAa,KAAK,SAAS;AACjC,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,UAAM,QAAQ,IAAI;AAClB,QACE,KAAK,KAAK,MAAM,KAChB,KAAK,QAAQ,CAAC,MAAM,KACpB,KAAK,QAAQ,CAAC,MAAM,KACpB,KAAK,QAAQ,CAAC,MAAM,KACpB;AACA;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,oBAAoB,SAAS;AACpC,MAAI;AAEJ,UAAI,wBAAQ,QAAQ,KAAK,GAAG;AAE1B,YAAQ,QAAQ;AAChB,UAAM,OAAO,QAAQ;AAErB,UAAM,gBAAgB;AACtB,YAAI,wBAAQ,QAAQ,cAAc,GAAG;AACnC,YAAM,OAAO,IAAI;AAAA,IACnB;AACA,UAAM,OAAO,IAAI;AAAA,EACnB,OAAO;AACL,YAAQ;AACR,UAAM,gBAAgB;AACtB,UAAM,OAAO;AAAA,EACf;AAEA,SAAO,MAAM,QAAQ,WAAW;AAClC;AAEA,SAASE,cAAa,GAAG;AACvB,SAAO,gCAAiB,gBAAgB,KAAK,SAAU,MAAM;AAC3D,WAAO,aAAa;AAAA,EACtB,CAAC;AACH;AAEA,SAASH,mBAAkB,OAAO;AAChC,MAAIG,cAAa,KAAK,GAAG;AACvB,WAAO,MAAM,UAAU,MAAM,KAAK,OAAO,CAAC;AAAA,EAC5C;AACA,SAAO;AACT;AAEA,SAAS,aAAa,MAAM,QAAQ,UAAU,aAAa;AACzD,QAAM,aAAa,oBAAoB,MAAM;AAM7C,MAAI,CAAC,CAAC,OAAO,WAAW;AACtB,WAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAEA,QAAM,KAAK,eAAO,MAAM,YAAY,QAAQ;AAC5C,QAAM,OAAO,cAAc,KAAK,CAAC;AAEjC,MAAI;AACJ,MAAI,CAAC,MAAM;AACT,cAAU,YACR,cAAc,KAAK,MACrB,cAAcH;AAAA,MACZ;AAAA,IACF,CAAC,6BAA6BA,mBAAkB,UAAU,CAAC;AAAA,EAC7D;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,oBAAoB,QAAQ,UAAU,GAAG,GAAG,OAAO,QAAQ;AAClE,QAAM,QAAQ;AACd,QAAM,iBAAiB,IAAI,0BAAW,GAAG,CAAC;AAC1C,QAAM,SAAS,MAAM,KAAK,gBAAgB,OAAO,MAAM;AAEvD,MAAI,CAAC,CAAC,OAAO,WAAW;AACtB,WAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAEA,MAAI,OAAO;AACX,MAAI;AAEJ,UAAI,wBAAQ,QAAQ,GAAG;AACrB,WAAO,OAAO,cAAc;AAAA,EAC9B,OAAO;AACL,WAAO,KAAC,wBAAQ,MAAM;AAAA,EACxB;AAEA,MAAI,CAAC,MAAM;AACT,cAAU,oBAAoB,QAAQ,iBAAiB,MAAM;AAAA,EAC/D;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,yBAAyB,QAAQ,UAAU,GAAG,GAAG,OAAO,QAAQ;AACvE,QAAM,QAAQ;AACd,QAAM,iBAAiB,IAAI,0BAAW,GAAG,CAAC;AAC1C,QAAM,SAAS,MAAM,UAAU,gBAAgB,QAAW,OAAO,MAAM;AAEvE,MAAI,CAAC,CAAC,OAAO,WAAW;AACtB,WAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAEA,MAAI,OAAO;AACX,MAAI;AAEJ,UAAI,wBAAQ,QAAQ,GAAG;AACrB,WAAO,OAAO,WAAW;AAAA,EAC3B,OAAO;AACL,WAAO,KAAC,wBAAQ,MAAM;AAAA,EACxB;AAEA,MAAI,CAAC,MAAM;AACT,cAAU,oBAAoB,QAAQ,iBAAiB,MAAM;AAAA,EAC/D;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,2BAA2B,SAAS;AAC3C,QAAM,UAAU,QAAQ;AACxB,MAAI,KAAK,QAAQ;AACjB,QAAM,KAAK,QAAQ;AACnB,MAAI,KAAK,QAAQ;AACjB,QAAM,aAAa,QAAQ;AAC3B,QAAM,cAAc,QAAQ;AAC5B,QAAM,QAAQ,QAAQ,SAAS;AAC/B,QAAM,QAAQ,QAAQ,SAAS;AAC/B,MAAI;AAEJ,MAAI,KAAC,wBAAQ,OAAO,GAAG;AACrB,UAAM,IAAI,8BAAe,8BAA8B;AAAA,EACzD;AAEA,MAAI,KAAC,wBAAQ,EAAE,KAAK,KAAC,wBAAQ,EAAE,GAAG;AAChC,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,UAAI,wBAAQ,EAAE,SAAK,wBAAQ,EAAE,GAAG;AAC9B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,UAAI,wBAAQ,EAAE,SAAK,wBAAQ,EAAE,GAAG;AAC9B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,MAAI,KAAC,wBAAQ,EAAE,GAAG;AAChB,QAAI,KAAC,wBAAQ,EAAE,GAAG;AAChB,WACE;AAAA,IACJ;AACA,SAAK,6BAAc,UAAU;AAAA,MAC3B;AAAA,MACA,oBAAoB;AAAA,MACpB,sBAAsB;AAAA,MACtB,oBAAoB;AAAA,QAClB,UAAU;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,KAAK,IAAI,2BAAY;AAAA,IACvB;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE,OAAO;AAAA,QACP,cAAc,sBAAO,mBAAmB;AAAA,UACtC;AAAA,UACA,YAAY,IAAI,aAAa,CAAC,GAAK,GAAK,OAAO,CAAG,CAAC;AAAA,UACnD,OAAO,2BAAY;AAAA,QACrB,CAAC;AAAA,QACD,wBAAwB;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,CAAC;AAED,MAAI,OAAO;AACT,gCAAa,IAAI,QAAQ,OAAO;AAChC,iBAAa,QAAQ,WAAW;AAAA,EAClC;AAEA,QAAM,UAAU,IAAI,2BAAY;AAAA,IAC9B,eAAe,6BAAc;AAAA,IAC7B,eAAe;AAAA,IACf,aAAa;AAAA,IACb;AAAA,IACA;AAAA,EACF,CAAC;AAED,UAAQ,QAAQ,OAAO;AACvB,QAAM,OAAO,QAAQ,WAAW;AAEhC,OAAK,GAAG,QAAQ;AAChB,OAAK,GAAG,QAAQ;AAEhB,SAAO;AAAA,IACL,OAAO;AAAA,IACP;AAAA,EACF;AACF;AAEA,SAAS,sBAAsB,QAAQ,UAAU,aAAa;AAC5D,QAAM,UAAU;AAChB,QAAM,UAAU,QAAQ;AACxB,QAAM,QAAQ,QAAQ,SAAS;AAC/B,QAAM,UAAU,QAAQ,WAAW;AAEnC,MAAI,KAAC,wBAAQ,QAAQ,GAAG;AACtB,eAAW,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,EAChC;AAEA,QAAMC,aAAY,CAAC,CAAC,OAAO;AAE3B,QAAM,SAAS,2BAA2B,OAAO;AAEjD,MAAI,OAAO;AACT,UAAM,cAAc,OAAO;AAC3B,QAAI,CAACA,YAAW;AACd,YAAM,gBAAgB,QAAQ,QAAQ,MAAM,QAAQ,IAAI;AACxD,UACE,YAAY,CAAC,MAAM,KACnB,YAAY,CAAC,MAAM,KACnB,YAAY,CAAC,MAAM,KACnB,YAAY,CAAC,MAAM,eACnB;AACA,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS,wEAAwE,aAAa,oBAAoB,WAAW;AAAA,QAC/H;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,OAAO,OAAO;AAEpB,MAAI,CAACA,YAAW;AACd,QAAI,aAAa;AACf,UACE,CAAC,eAAAC,KAAW,cAAc,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,OAAO,KAC1D,CAAC,eAAAA,KAAW,cAAc,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,OAAO,KAC1D,CAAC,eAAAA,KAAW,cAAc,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,OAAO,KAC1D,CAAC,eAAAA,KAAW,cAAc,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,OAAO,GAC1D;AACA,eAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS,8BAA8B,QAAQ,mBAAmB,IAAI;AAAA,QACxE;AAAA,MACF;AAAA,IACF,WACE,eAAAA,KAAW,cAAc,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,OAAO,KACzD,eAAAA,KAAW,cAAc,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,OAAO,KACzD,eAAAA,KAAW,cAAc,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,OAAO,KACzD,eAAAA,KAAW,cAAc,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG,GAAG,OAAO,GACzD;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,kCAAkC,QAAQ,mBAAmB,IAAI;AAAA,MAC5E;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,EACR;AACF;AAEA,SAAS,mBAAmB,OAAO;AACjC,SAAO,WAAY;AACjB,SAAK,YAAY,sBAAsB,KAAK,CAAC;AAC7C,SAAK,iBAAiB,2BAA2B,KAAK,CAAC;AAAA,EACzD;AACF;AACA,IAAO,6BAAQ;;;AEhhCf,IAAAE,iBAAwB;AAExB,SAAS,2BAA2B,GAAG,GAAG;AACxC,MAAI;AAEJ,MAAI,MAAM,YAAQ,wBAAQ,CAAC,GAAG;AAC5B,QAAI,OAAO,EAAE,WAAW,YAAY;AAClC,aAAO,EAAE,OAAO,CAAC;AAAA,IACnB,WAAW,aAAa,QAAQ;AAE9B,eAAS,OAAO,eAAe,CAAC,EAAE,YAAY;AAC9C,UAAI,OAAO,WAAW,YAAY;AAChC,eAAO,OAAO,GAAG,CAAC;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,MAAM,YAAQ,wBAAQ,CAAC,GAAG;AAC5B,QAAI,OAAO,EAAE,WAAW,YAAY;AAClC,aAAO,EAAE,OAAO,CAAC;AAAA,IACnB,WAAW,aAAa,QAAQ;AAE9B,eAAS,OAAO,eAAe,CAAC,EAAE,YAAY;AAC9C,UAAI,OAAO,WAAW,YAAY;AAChC,eAAO,OAAO,GAAG,CAAC;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AAGA,SAAO;AACT;AACA,IAAO,qCAAQ;;;AC7Bf,SAAS,iBACP,KACA,kBACA,kBACAC,kBACAC,YACAC,UACAC,mBACAC,oBACA;AAEA,SAAO,mBAAmB;AAE1B,SAAO,oBAAoBF;AAE3B,QAAM,mBAAmB,OAAO;AAEhC,SAAO,WAAW,SAAU,MAAM,OAAO,UAAU;AAGjD,QACE,oBACA,qBAAqB,MACrB,qBAAqB,UACrB,aAAa,kBACb;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B,WACE,oBACA,qBAAqB,MACrB,aAAa,kBACb;AACA,aAAO,UAAU,MAAM,KAAK;AAAA,IAC9B,OAAO;AACL,uBAAiB,MAAM,KAAK;AAAA,IAC9B;AAAA,EACF;AAEA,MAAIF,kBAAiB;AACnB,WAAO,kBAAkB;AAAA,EAC3B;AAEA,MAAIC,YAAW;AACb,WAAO,YAAY;AAAA,EACrB;AAEA,SAAO,mBAAmBE;AAC1B,SAAO,oBAAoBC;AAE3B,MAAI,WAAW,WAAY;AACzB,+BAAmB,CAACF,QAAO,EAAE,KAAK,GAAG;AACrC,QAAI,wBAAwB,kCAA0B;AAAA,EACxD,CAAC;AACH;AACA,IAAO,2BAAQ;;;ACtDf,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,IAAI;AACJ,IAAI;AAEJ,IAAI,UAAU,OAAO,MAAM;AACzB,oBAAkB,UAAU,OAAO,KAAK,CAAC;AACzC,oBAAkB,UAAU,OAAO,KAAK,CAAC;AACzC,oBAAkB,UAAU,OAAO,KAAK,CAAC;AACzC,cAAY,UAAU,OAAO,KAAK,CAAC;AACnC,YAAU,UAAU,OAAO,KAAK,CAAC;AACjC,qBAAmB,UAAU,OAAO,KAAK,CAAC;AAC1C,sBAAoB,UAAU,OAAO,KAAK,CAAC;AAC7C;AAEA,IAAI,SAAS;AACX,SAAO,kBAAkB;AAC3B,OAAO;AACL,SAAO,kBAAkB;AAC3B;AAEA,QAAQ,2BAA2B;AACnC;AAAA,EACE,QAAQ,OAAO;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": ["import_engine", "typedArrayToArray", "webglStub", "CesiumMath", "isTypedArray", "import_engine", "webglValidation", "webglStub", "release", "debugCanvasWidth", "debugCanvasHeight"]}